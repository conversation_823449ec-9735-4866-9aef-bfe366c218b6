# Dashboard API Documentation

## Base URL
```
http://localhost:8003/api/v1/dashboard
```

## Overview

Dashboard API cung cấp các endpoints để giám sát và phân tích hệ thống Tool Restore Database với 2 bảng chính:
- **ToolRestoreSQLDatabase**: <PERSON><PERSON><PERSON> trữ thông tin các lần restore thực tế
- **SetupToolRestoreSQLDatabase**: <PERSON><PERSON><PERSON> trữ cấu hình setup cho restore

## Common Response Format

```typescript
interface ApiResponse<T> {
  code: number;      // HTTP status code
  data: T;           // Response data
  message: string;   // Response message
}
```

## API Endpoints

### 1. Overview Dashboard

#### GET /overview
Lấy thống kê tổng quan hệ thống và hoạt động gần đây.

**Response:**
```typescript
interface OverviewResponse {
  stats: {
    totalRestoreOperations: number;
    successfulRestores: number;
    failedRestores: number;
    successRate: number;
    totalSetupConfigs: number;
    autoConfigs: number;
    manualConfigs: number;
    uniqueServersWithRestores: number;
    uniqueServersWithSetup: number;
    uniqueDatabasesRestored: number;
    uniqueDatabasesConfigured: number;
  };
  activity: Array<{
    activityType: string;
    count: number;
    successCount: number;
    failureCount: number;
  }>;
}
```

#### GET /summary?days=30
Tổng hợp tất cả thông tin dashboard chính.

**Parameters:**
- `days` (optional): Số ngày để phân tích (default: 30)

**Response:**
```typescript
interface SummaryResponse {
  overview: OverviewStats;
  recentActivity: RecentActivity[];
  topDatabases: TopDatabase[];
  alerts: Alert[];
  systemHealth: SystemHealth;
}
```

### 2. Restore Operations

#### GET /restore-trend?days=30
Xu hướng restore theo ngày.

**Parameters:**
- `days` (optional): Số ngày (default: 30)

**Response:**
```typescript
interface DailyTrend {
  date: string;                // Format: dd/MM/yyyy
  restoreDate: string;         // ISO date string
  totalRestores: number;
  successCount: number;
  failureCount: number;
  dailySuccessRate: number;
  avgSizeBAK: number;
  avgSizeZip: number;
}
```

#### GET /top-databases?days=30&limit=10
Top databases được restore nhiều nhất.

**Parameters:**
- `days` (optional): Số ngày (default: 30)
- `limit` (optional): Số lượng kết quả (default: 10)

**Response:**
```typescript
interface TopDatabase {
  databaseName: string;
  restoreCount: number;
  successCount: number;
  failureCount: number;
  successRate: number;
  avgSizeBAK: number;
  maxSizeBAK: number;
  lastRestoreTime: string;     // ISO datetime
  hoursSinceLastRestore: number;
}
```

#### GET /server-stats?days=30
Thống kê theo server/IP.

**Parameters:**
- `days` (optional): Số ngày (default: 30)

**Response:**
```typescript
interface ServerStats {
  ip: string;
  totalRestores: number;
  successCount: number;
  failureCount: number;
  successRate: number;
  uniqueDbCount: number;
  totalRestoredSizeMB: number;
  firstRestore: string;        // ISO datetime
  lastRestore: string;         // ISO datetime
}
```

### 3. Setup Configuration

#### GET /setup-stats
Thống kê cấu hình setup.

**Response:**
```typescript
interface SetupConfigStats {
  ip: string;
  serverSQL: string;
  configCount: number;
  autoConfigCount: number;
  manualConfigCount: number;
  autoPercentage: number;
  uniqueDbConfigured: number;
  firstConfigTime: string;     // ISO datetime
  lastConfigTime: string;      // ISO datetime
  actualRestoreCount: number;
}
```

#### GET /config-usage
So sánh setup vs actual restore.

**Response:**
```typescript
interface ConfigUsageComparison {
  status: 'CONFIGURED_BUT_NOT_USED' | 'USED_WITHOUT_CONFIG';
  ip: string;
  databaseName: string;
  serverSQL: string;
  configTime: string;          // ISO datetime
  configType: 'Auto' | 'Manual' | 'Unknown';
}
```

#### GET /config-effectiveness
Phân tích hiệu quả cấu hình.

**Response:**
```typescript
interface ConfigEffectiveness {
  ip: string;
  databaseName: string;
  serverSQL: string;
  configType: 'Auto' | 'Manual';
  configTime: string;          // ISO datetime
  restoreCount: number;
  successfulRestores: number;
  effectivenessRating: 'Not Used' | 'Low Effectiveness' | 'Moderately Effective' | 'Effective' | 'Highly Effective';
  successRate: number;
}
```

### 4. Performance Dashboard

#### GET /hourly-performance?days=7
Hiệu suất theo giờ trong ngày.

**Parameters:**
- `days` (optional): Số ngày (default: 7)

**Response:**
```typescript
interface HourlyPerformance {
  hourOfDay: number;           // 0-23
  hourLabel: string;           // "00:00", "01:00", etc.
  restoreCount: number;
  successCount: number;
  failureCount: number;
  avgSizeBAK: number;
  timePeriod: 'Business Hours' | 'Evening' | 'Night' | 'Early Morning';
}
```

#### GET /size-distribution?days=30
Phân bố kích thước file.

**Parameters:**
- `days` (optional): Số ngày (default: 30)

**Response:**
```typescript
interface SizeDistribution {
  sizeCategory: 'Small (≤100MB)' | 'Medium (100MB-1GB)' | 'Large (1GB-10GB)' | 'Very Large (10GB-50GB)' | 'Huge (>50GB)';
  restoreCount: number;
  successCount: number;
  successRate: number;
  avgSizeMB: number;
  minSizeMB: number;
  maxSizeMB: number;
}
```

### 5. Alerts & Monitoring

#### GET /alerts
Danh sách cảnh báo hệ thống.

**Response:**
```typescript
interface Alert {
  alertType: 'RECENT_FAILURES' | 'HIGH_FAILURE_RATE' | 'UNUSED_CONFIGS';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  title: string;
  count: number;
  details: string;             // Comma-separated details
  alertTime: string;           // ISO datetime
}
```

#### GET /health-check
Kiểm tra sức khỏe hệ thống.

**Response:**
```typescript
interface SystemHealth {
  checkType: 'SYSTEM_HEALTH';
  restoresLast24h: number;
  successfulRestoresLast24h: number;
  newConfigsLast24h: number;
  activeServersLast7Days: number;
  dataQualityIssues: number;
  checkTime: string;           // ISO datetime
}
```

### 6. Detailed Analysis

#### GET /restore-details
Chi tiết restore với pagination và filtering.

**Parameters:**
- `page` (optional): Trang hiện tại (default: 1)
- `size` (optional): Kích thước trang (default: 50)
- `days` (optional): Số ngày (default: 30)
- `database` (optional): Filter theo tên database
- `ip` (optional): Filter theo IP
- `status` (optional): Filter theo trạng thái ("success" hoặc "failed")
- `sort` (optional): Trường sắp xếp (default: "DateTimeRestore")
- `order` (optional): Thứ tự sắp xếp ("asc" hoặc "desc", default: "desc")

**Response:**
```typescript
interface PaginatedRestoreDetails {
  data: Array<{
    id: string;
    ip: string;
    databaseName: string;
    sizeFileZip: string;
    sizeBAK: string;
    dateTimeRestore: string;   // ISO datetime
    status: 'Success' | 'Failed';
    compressionRatio: number;
    serverSQL: string;
    configType: 'Auto' | 'Manual';
    destinationFolder: string;
    logSummary: string;
    hoursSinceRestore: number;
  }>;
  pagination: {
    totalRecords: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
}
```

### 7. Utility

#### POST /refresh
Trigger manual refresh của dashboard data.

**Response:**
```json
{
  "code": 200,
  "data": null,
  "message": "Dashboard data refresh triggered successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "code": 400,
  "data": null,
  "message": "Invalid query parameters"
}
```

### 500 Internal Server Error
```json
{
  "code": 500,
  "data": null,
  "message": "Database connection error: ..."
}
```

## Usage Examples

### JavaScript/Axios
```javascript
// Get overview data
const overview = await axios.get('/api/v1/dashboard/overview');

// Get restore trend for last 7 days
const trend = await axios.get('/api/v1/dashboard/restore-trend?days=7');

// Get top 5 databases
const topDbs = await axios.get('/api/v1/dashboard/top-databases?limit=5');

// Get restore details with pagination
const details = await axios.get('/api/v1/dashboard/restore-details?page=1&size=20&status=failed');
```

### Vue.js Composable
```typescript
export function useDashboard() {
  const apiClient = axios.create({
    baseURL: '/api/v1/dashboard'
  });

  const getOverview = () => apiClient.get('/overview');
  const getRestoreTrend = (days = 30) => apiClient.get(`/restore-trend?days=${days}`);
  const getTopDatabases = (days = 30, limit = 10) => 
    apiClient.get(`/top-databases?days=${days}&limit=${limit}`);
  
  return {
    getOverview,
    getRestoreTrend,
    getTopDatabases,
    // ... other methods
  };
}
```

## Performance Considerations

1. **Caching**: Các endpoints có thể được cache trong 5-15 phút
2. **Pagination**: Sử dụng pagination cho endpoints trả về nhiều dữ liệu
3. **Filtering**: Sử dụng parameters để giới hạn dữ liệu trả về
4. **Indexing**: Đảm bảo database có indexes phù hợp cho performance

## Rate Limiting

- **Overview endpoints**: 60 requests/minute
- **Detail endpoints**: 30 requests/minute
- **Refresh endpoint**: 5 requests/minute
