# Dashboard Specification - Tool Restore Database Management

## 📊 Dashboard Overview

Dashboard này được thiết kế để giám sát và quản lý hệ thống restore database với 2 bảng chính:
- **ToolRestoreSQLDatabase**: <PERSON><PERSON><PERSON> trữ thông tin các lần restore thực tế
- **SetupToolRestoreSQLDatabase**: <PERSON><PERSON><PERSON> trữ cấu hình setup cho restore

## 🎯 Dashboard Pages

### 1. Overview Dashboard (Trang chính)
**URL**: `/dashboard/overview`

#### KPI Cards (Hàng đầu)
```typescript
interface OverviewKPIs {
  totalRestores: number;
  successRate: number;        // Percentage
  activeServers: number;      // Unique IPs with activity
  totalConfigs: number;
  recentFailures: number;     // Last 24h
  avgCompressionRatio: number; // Percentage
}
```

#### Charts & Widgets
1. **Daily Restore Trend** (Line Chart)
   - X-axis: Dates (last 30 days)
   - Y-axis: Number of restores
   - Series: Success, Failed, Total

2. **Success Rate Pie Chart**
   - Success vs Failed restores

3. **Recent Activity Table**
   - Last 10 restore operations
   - Columns: Time, Database, Server, Status, Size

4. **Top Databases Table**
   - Most frequently restored databases
   - Columns: Database, Count, Success Rate, Last Restore

### 2. Restore Operations Dashboard
**URL**: `/dashboard/restores`

#### Main Components
1. **30-Day Trend Chart** (Line Chart with multiple series)
   - Daily restore counts
   - Success/failure trends
   - Average size trends

2. **Server Performance Table**
   ```typescript
   interface ServerStats {
     ip: string;
     totalRestores: number;
     successRate: number;
     uniqueDbCount: number;
     totalSizeMB: number;
     lastRestore: string;
   }
   ```

3. **Database Rankings**
   - Top databases by restore frequency
   - Success rates per database
   - Size statistics

4. **Hourly Performance Heatmap**
   - X-axis: Hours (0-23)
   - Y-axis: Days of week
   - Color intensity: Number of restores

### 3. Configuration Management Dashboard
**URL**: `/dashboard/configurations`

#### Key Features
1. **Configuration Overview Cards**
   - Total configurations
   - Auto vs Manual ratio
   - Unused configurations count
   - Configuration effectiveness score

2. **Setup vs Usage Analysis**
   ```typescript
   interface ConfigAnalysis {
     configuredButNotUsed: ConfigItem[];
     usedWithoutConfig: RestoreItem[];
     effectiveConfigs: ConfigItem[];
   }
   ```

3. **Configuration Effectiveness Chart**
   - Bar chart showing config usage
   - Success rates per configuration

4. **Server Configuration Matrix**
   - Table showing all servers and their configurations
   - Auto/Manual indicators
   - Last usage timestamps

### 4. Performance Analysis Dashboard
**URL**: `/dashboard/performance`

#### Analytics Components
1. **Size Distribution Chart** (Bar Chart)
   - Categories: Small, Medium, Large, Very Large, Huge
   - Success rates per size category

2. **Hourly Performance Chart**
   - Business hours vs off-hours performance
   - Average restore times by hour

3. **Compression Analysis**
   - Compression ratio trends
   - Size before/after comparison

4. **Performance Metrics Table**
   ```typescript
   interface PerformanceMetrics {
     timePeriod: string;
     avgRestoreTime: number;
     avgSize: number;
     successRate: number;
     compressionRatio: number;
   }
   ```

### 5. Alerts & Monitoring Dashboard
**URL**: `/dashboard/alerts`

#### Alert Types
```typescript
interface Alert {
  type: 'RECENT_FAILURES' | 'HIGH_FAILURE_RATE' | 'UNUSED_CONFIGS' | 'LARGE_FILES';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  title: string;
  count: number;
  details: string;
  timestamp: string;
}
```

#### Components
1. **Critical Alerts Panel**
   - Real-time alerts
   - Color-coded by priority
   - Expandable details

2. **System Health Status**
   - Overall system health score
   - Key metrics indicators
   - Trend arrows (up/down/stable)

3. **Recent Failures Table**
   - Failed restores in last 24h
   - Error details and patterns

4. **Monitoring Charts**
   - Failure rate trends
   - Alert frequency over time

## 🔧 Technical Implementation

### Frontend Components (Vue.js + TypeScript)

#### 1. Dashboard Layout Component
```vue
<template>
  <div class="dashboard-layout">
    <DashboardHeader :title="pageTitle" />
    <DashboardSidebar :activeRoute="currentRoute" />
    <main class="dashboard-content">
      <router-view />
    </main>
  </div>
</template>
```

#### 2. KPI Card Component
```vue
<template>
  <div class="kpi-card">
    <div class="kpi-icon">
      <component :is="iconComponent" />
    </div>
    <div class="kpi-content">
      <h3 class="kpi-value">{{ formattedValue }}</h3>
      <p class="kpi-label">{{ label }}</p>
      <div class="kpi-trend" :class="trendClass">
        <TrendIcon :direction="trend" />
        <span>{{ trendText }}</span>
      </div>
    </div>
  </div>
</template>
```

#### 3. Chart Components
```typescript
// Using Chart.js or similar library
interface ChartProps {
  data: ChartData;
  options: ChartOptions;
  type: 'line' | 'bar' | 'pie' | 'doughnut';
  height?: number;
}
```

### API Integration

#### 1. Dashboard Service
```typescript
class DashboardService {
  async getOverviewStats(): Promise<OverviewKPIs> {
    return this.apiClient.get('/api/dashboard/overview');
  }

  async getRestoreTrend(days: number = 30): Promise<TrendData[]> {
    return this.apiClient.get(`/api/dashboard/restore-trend?days=${days}`);
  }

  async getAlerts(): Promise<Alert[]> {
    return this.apiClient.get('/api/dashboard/alerts');
  }

  // ... other methods
}
```

#### 2. Real-time Updates
```typescript
// Using WebSocket or Server-Sent Events
class DashboardRealtime {
  private eventSource: EventSource;

  connect() {
    this.eventSource = new EventSource('/api/dashboard/stream');
    this.eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleRealtimeUpdate(data);
    };
  }

  private handleRealtimeUpdate(data: RealtimeUpdate) {
    // Update dashboard components
    this.updateKPIs(data.kpis);
    this.updateAlerts(data.alerts);
  }
}
```

### State Management (Pinia)

```typescript
export const useDashboardStore = defineStore('dashboard', () => {
  const overviewData = ref<OverviewKPIs | null>(null);
  const alerts = ref<Alert[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const fetchOverviewData = async () => {
    loading.value = true;
    try {
      overviewData.value = await dashboardService.getOverviewStats();
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const refreshInterval = ref<NodeJS.Timeout | null>(null);

  const startAutoRefresh = (intervalMs: number = 300000) => { // 5 minutes
    refreshInterval.value = setInterval(fetchOverviewData, intervalMs);
  };

  return {
    overviewData: readonly(overviewData),
    alerts: readonly(alerts),
    loading: readonly(loading),
    error: readonly(error),
    fetchOverviewData,
    startAutoRefresh,
  };
});
```

## 🎨 UI/UX Design Guidelines

### Color Scheme
```css
:root {
  --color-success: #10B981;
  --color-error: #EF4444;
  --color-warning: #F59E0B;
  --color-info: #3B82F6;
  --color-neutral: #6B7280;
  --color-background: #F9FAFB;
  --color-surface: #FFFFFF;
  --color-border: #E5E7EB;
}
```

### Responsive Breakpoints
```css
/* Mobile First Approach */
.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### Animation & Transitions
```css
.kpi-card {
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 📱 Mobile Optimization

### Mobile-First Components
1. **Collapsible Sidebar**: Hamburger menu for mobile
2. **Swipeable Charts**: Touch-friendly chart interactions
3. **Responsive Tables**: Horizontal scroll or card layout
4. **Touch-Optimized Buttons**: Minimum 44px touch targets

### Performance Considerations
1. **Lazy Loading**: Load charts only when visible
2. **Data Pagination**: Limit initial data load
3. **Image Optimization**: Use WebP format for icons
4. **Bundle Splitting**: Separate dashboard chunks

## 🔄 Auto-Refresh Strategy

### Refresh Intervals
- **Overview Dashboard**: 5 minutes
- **Alerts Dashboard**: 1 minute  
- **Performance Dashboard**: 15 minutes
- **Historical Data**: 1 hour

### Smart Refresh Logic
```typescript
class SmartRefresh {
  private intervals = new Map<string, NodeJS.Timeout>();

  setRefreshInterval(key: string, callback: () => void, ms: number) {
    this.clearInterval(key);
    this.intervals.set(key, setInterval(callback, ms));
  }

  pauseWhenInactive() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseAll();
      } else {
        this.resumeAll();
      }
    });
  }
}
```

---

**Lưu ý**: Dashboard này được thiết kế để có thể mở rộng và tùy chỉnh theo nhu cầu cụ thể của từng tổ chức.
