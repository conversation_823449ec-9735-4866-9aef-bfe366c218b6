USE [IT]
GO

/****** Object:  Table [dbo].[ToolRestoreSQLDatabase]    Script Date: 08/08/2025 12:47:34 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ToolRestoreSQLDatabase](
	[ID] [uniqueidentifier] NOT NULL,
	[IP] [nvarchar](50) NULL,
	[DatabaseName] [nvarchar](255) NULL,
	[<PERSON>zeFileZip] [nchar](10) NULL,
	[SizeBAK] [nchar](10) NULL,
	[DateTimeRestore] [smalldatetime] NULL,
	[isSuccess] [nvarchar](50) NULL,
	[LogContent] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO




USE [IT]
GO

/****** Object:  Table [dbo].[SetupToolRestoreSQLDatabase]    Script Date: 08/08/2025 12:47:52 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SetupToolRestoreSQLDatabase](
	[ID] [uniqueidentifier] NOT NULL,
	[IP] [nvarchar](50) NULL,
	[DatabaseName] [nvarchar](max) NULL,
	[ServerSQL] [nvarchar](50) NULL,
	[UserSQL] [nvarchar](50) NULL,
	[PasswordSQL] [nvarchar](50) NULL,
	[Destination_folder] [nvarchar](max) NULL,
	[Source_File] [nvarchar](max) NULL,
	[Folder_Logical] [nvarchar](max) NULL,
	[DateTimeRestore] [smalldatetime] NULL,
	[isAuto] [nchar](10) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- =============================================
-- DASHBOARD SQL QUERIES
-- Tool Restore Database Management System
-- =============================================

-- =============================================
-- 1. OVERVIEW DASHBOARD - Thống kê tổng quan
-- =============================================

-- 1.1 Thống kê tổng quan hệ thống
SELECT
    -- Thống kê Restore Operations
    (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
    (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
    (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,

    -- Tỷ lệ thành công
    CASE
        WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0
        THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 /
                  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
        ELSE 0
    END as SuccessRate,

    -- Thống kê Setup Configurations
    (SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
    (SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
    (SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,

    -- Thống kê Server/IP
    (SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
    (SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,

    -- Thống kê Database
    (SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
    (SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured;

-- 1.2 Hoạt động gần đây (24h qua)
SELECT
    'RESTORE_ACTIVITY' as ActivityType,
    COUNT(*) as Count,
    SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
FROM [dbo].[ToolRestoreSQLDatabase]
WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

UNION ALL

SELECT
    'SETUP_ACTIVITY' as ActivityType,
    COUNT(*) as Count,
    SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as AutoCount,
    SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as ManualCount
FROM [dbo].[SetupToolRestoreSQLDatabase]
WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE());

-- =============================================
-- 2. RESTORE OPERATIONS DASHBOARD
-- =============================================

-- 2.1 Xu hướng restore theo ngày (30 ngày qua)
WITH DateRange AS (
    SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
    FROM master..spt_values
    WHERE type = 'p' AND number <= 29
)
SELECT
    FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
    dr.RestoreDate,
    ISNULL(COUNT(t.ID), 0) as TotalRestores,
    ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
    ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,

    -- Tính tỷ lệ thành công
    CASE
        WHEN COUNT(t.ID) > 0
        THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
        ELSE 0
    END as DailySuccessRate,

    -- Thống kê size (convert từ string sang numeric)
    ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
    ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip

FROM DateRange dr
LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t
    ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
GROUP BY dr.RestoreDate
ORDER BY dr.RestoreDate;

-- 2.2 Top Databases được restore nhiều nhất
SELECT TOP 10
    t.DatabaseName,
    COUNT(*) as RestoreCount,
    SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,

    -- Tỷ lệ thành công
    CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,

    -- Size statistics
    AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
    MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,

    -- Thời gian restore gần nhất
    MAX(t.DateTimeRestore) as LastRestoreTime,
    DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore

FROM [dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
GROUP BY t.DatabaseName
ORDER BY RestoreCount DESC;

-- 2.3 Thống kê theo Server/IP
SELECT
    t.IP,
    COUNT(*) as TotalRestores,
    SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,

    -- Tỷ lệ thành công
    CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,

    -- Số database unique trên server
    COUNT(DISTINCT t.DatabaseName) as UniqueDbCount,

    -- Tổng dung lượng đã restore thành công
    SUM(CASE WHEN t.isSuccess = '1' THEN TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT) ELSE 0 END) as TotalRestoredSizeMB,

    -- Thời gian hoạt động
    MIN(t.DateTimeRestore) as FirstRestore,
    MAX(t.DateTimeRestore) as LastRestore

FROM [dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
GROUP BY t.IP
ORDER BY TotalRestores DESC;

-- =============================================
-- 3. SETUP CONFIGURATION DASHBOARD
-- =============================================

-- 3.1 Thống kê Setup Configurations
SELECT
    s.IP,
    s.ServerSQL,
    COUNT(*) as ConfigCount,
    SUM(CASE WHEN s.isAuto = '1' THEN 1 ELSE 0 END) as AutoConfigCount,
    SUM(CASE WHEN s.isAuto = '0' THEN 1 ELSE 0 END) as ManualConfigCount,

    -- Tỷ lệ tự động
    CAST(SUM(CASE WHEN s.isAuto = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as AutoPercentage,

    -- Số database được cấu hình
    COUNT(DISTINCT s.DatabaseName) as UniqueDbConfigured,

    -- Thời gian cấu hình
    MIN(s.DateTimeRestore) as FirstConfigTime,
    MAX(s.DateTimeRestore) as LastConfigTime,

    -- Kiểm tra có restore thực tế không
    (SELECT COUNT(*)
     FROM [dbo].[ToolRestoreSQLDatabase] t
     WHERE t.IP = s.IP) as ActualRestoreCount

FROM [dbo].[SetupToolRestoreSQLDatabase] s
GROUP BY s.IP, s.ServerSQL
ORDER BY ConfigCount DESC;

-- 3.2 So sánh Setup vs Actual Restore
SELECT
    'CONFIGURED_BUT_NOT_USED' as Status,
    s.IP,
    s.DatabaseName,
    s.ServerSQL,
    s.DateTimeRestore as ConfigTime,
    CASE WHEN s.isAuto = '1' THEN 'Auto' ELSE 'Manual' END as ConfigType
FROM [dbo].[SetupToolRestoreSQLDatabase] s
LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
WHERE t.ID IS NULL

UNION ALL

SELECT
    'USED_WITHOUT_CONFIG' as Status,
    t.IP,
    t.DatabaseName,
    'N/A' as ServerSQL,
    t.DateTimeRestore as ConfigTime,
    'Unknown' as ConfigType
FROM [dbo].[ToolRestoreSQLDatabase] t
LEFT JOIN [dbo].[SetupToolRestoreSQLDatabase] s ON t.IP = s.IP AND t.DatabaseName = s.DatabaseName
WHERE s.ID IS NULL
ORDER BY ConfigTime DESC;

-- 3.3 Configuration Effectiveness Analysis
WITH ConfigUsage AS (
    SELECT
        s.IP,
        s.DatabaseName,
        s.ServerSQL,
        s.isAuto,
        s.DateTimeRestore as ConfigTime,
        COUNT(t.ID) as RestoreCount,
        SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessfulRestores
    FROM [dbo].[SetupToolRestoreSQLDatabase] s
    LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t
        ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
        AND t.DateTimeRestore >= s.DateTimeRestore
    GROUP BY s.IP, s.DatabaseName, s.ServerSQL, s.isAuto, s.DateTimeRestore
)
SELECT
    IP,
    DatabaseName,
    ServerSQL,
    CASE WHEN isAuto = '1' THEN 'Auto' ELSE 'Manual' END as ConfigType,
    ConfigTime,
    RestoreCount,
    SuccessfulRestores,

    -- Effectiveness metrics
    CASE
        WHEN RestoreCount = 0 THEN 'Not Used'
        WHEN SuccessfulRestores = RestoreCount THEN 'Highly Effective'
        WHEN SuccessfulRestores > RestoreCount * 0.8 THEN 'Effective'
        WHEN SuccessfulRestores > RestoreCount * 0.5 THEN 'Moderately Effective'
        ELSE 'Low Effectiveness'
    END as EffectivenessRating,

    CASE
        WHEN RestoreCount > 0
        THEN CAST(SuccessfulRestores * 100.0 / RestoreCount AS DECIMAL(5,2))
        ELSE 0
    END as SuccessRate

FROM ConfigUsage
ORDER BY RestoreCount DESC, SuccessfulRestores DESC;

-- =============================================
-- 4. PERFORMANCE & MONITORING DASHBOARD
-- =============================================

-- 4.1 Hiệu suất theo giờ trong ngày
WITH Hours AS (
    SELECT number as HourOfDay
    FROM master..spt_values
    WHERE type = 'p' AND number BETWEEN 0 AND 23
)
SELECT
    h.HourOfDay,
    FORMAT(h.HourOfDay, '00') + ':00' as HourLabel,

    -- Restore statistics
    ISNULL(COUNT(t.ID), 0) as RestoreCount,
    ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
    ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,

    -- Average size during this hour
    ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,

    -- Performance classification
    CASE
        WHEN h.HourOfDay BETWEEN 8 AND 17 THEN 'Business Hours'
        WHEN h.HourOfDay BETWEEN 18 AND 22 THEN 'Evening'
        WHEN h.HourOfDay BETWEEN 23 AND 6 THEN 'Night'
        ELSE 'Early Morning'
    END as TimePeriod

FROM Hours h
LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t
    ON DATEPART(HOUR, t.DateTimeRestore) = h.HourOfDay
    AND t.DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
GROUP BY h.HourOfDay
ORDER BY h.HourOfDay;

-- 4.2 Size Distribution Analysis
SELECT
    CASE
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 100 THEN 'Small (≤100MB)'
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 1000 THEN 'Medium (100MB-1GB)'
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 10000 THEN 'Large (1GB-10GB)'
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 50000 THEN 'Very Large (10GB-50GB)'
        ELSE 'Huge (>50GB)'
    END as SizeCategory,

    COUNT(*) as RestoreCount,
    SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
    CAST(SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,

    AVG(TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeMB,
    MIN(TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT)) as MinSizeMB,
    MAX(TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeMB

FROM [dbo].[ToolRestoreSQLDatabase]
WHERE DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
    AND TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) IS NOT NULL
GROUP BY
    CASE
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 100 THEN 'Small (≤100MB)'
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 1000 THEN 'Medium (100MB-1GB)'
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 10000 THEN 'Large (1GB-10GB)'
        WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 50000 THEN 'Very Large (10GB-50GB)'
        ELSE 'Huge (>50GB)'
    END
ORDER BY AvgSizeMB;

-- =============================================
-- 5. ALERTS & MONITORING DASHBOARD
-- =============================================

-- 5.1 Critical Alerts
SELECT
    'RECENT_FAILURES' as AlertType,
    'HIGH' as Priority,
    'Restore failures trong 24h qua' as AlertTitle,
    COUNT(*) as Count,
    STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
    GETDATE() as AlertTime
FROM [dbo].[ToolRestoreSQLDatabase]
WHERE isSuccess = '0'
    AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
HAVING COUNT(*) > 0

UNION ALL

SELECT
    'HIGH_FAILURE_RATE' as AlertType,
    'MEDIUM' as Priority,
    'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as AlertTitle,
    COUNT(*) as Count,
    STRING_AGG(DatabaseName, ', ') as Details,
    GETDATE() as AlertTime
FROM (
    SELECT
        DatabaseName,
        COUNT(*) as TotalRestores,
        SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
        CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
    FROM [dbo].[ToolRestoreSQLDatabase]
    WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
    GROUP BY DatabaseName
    HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
) HighFailureDb
HAVING COUNT(*) > 0

UNION ALL

SELECT
    'UNUSED_CONFIGS' as AlertType,
    'LOW' as Priority,
    'Cấu hình setup chưa được sử dụng' as AlertTitle,
    COUNT(*) as Count,
    STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
    GETDATE() as AlertTime
FROM [dbo].[SetupToolRestoreSQLDatabase] s
LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
WHERE t.ID IS NULL
    AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
HAVING COUNT(*) > 0;

-- 5.2 System Health Check
SELECT
    'SYSTEM_HEALTH' as CheckType,

    -- Restore health
    (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
     WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

    (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
     WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

    -- Setup health
    (SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
     WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

    -- Active servers
    (SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
     WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

    -- Data quality
    (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
     WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

    GETDATE() as CheckTime;

-- =============================================
-- 6. DETAILED ANALYSIS QUERIES
-- =============================================

-- 6.1 Comprehensive Restore Details với Pagination
SELECT
    t.ID,
    t.IP,
    t.DatabaseName,
    t.SizeFileZip,
    t.SizeBAK,
    t.DateTimeRestore,
    CASE WHEN t.isSuccess = '1' THEN 'Success' ELSE 'Failed' END as Status,

    -- Compression ratio
    CASE
        WHEN TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT) > 0
             AND TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT) > 0
        THEN CAST(((TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT) -
                    TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)) * 100.0 /
                   TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) AS DECIMAL(5,2))
        ELSE 0
    END as CompressionRatio,

    -- Setup info if available
    s.ServerSQL,
    CASE WHEN s.isAuto = '1' THEN 'Auto' ELSE 'Manual' END as ConfigType,
    s.Destination_folder,

    -- Log summary
    CASE
        WHEN LEN(t.LogContent) > 100 THEN LEFT(t.LogContent, 100) + '...'
        ELSE t.LogContent
    END as LogSummary,

    -- Time since restore
    DATEDIFF(HOUR, t.DateTimeRestore, GETDATE()) as HoursSinceRestore

FROM [dbo].[ToolRestoreSQLDatabase] t
LEFT JOIN [dbo].[SetupToolRestoreSQLDatabase] s
    ON t.IP = s.IP AND t.DatabaseName = s.DatabaseName
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
ORDER BY t.DateTimeRestore DESC
OFFSET 0 ROWS FETCH NEXT 50 ROWS ONLY; -- Pagination: thay đổi OFFSET và FETCH theo cần thiết

-- 6.2 Count query for pagination
SELECT COUNT(*) as TotalRecords
FROM [dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE());

-- =============================================
-- 7. DASHBOARD API ENDPOINTS MAPPING
-- =============================================

/*
Suggested API Endpoints for Dashboard:

1. Overview Dashboard:
   GET /api/dashboard/overview
   - Query 1.1: System overview statistics
   - Query 1.2: Recent activity (24h)

2. Restore Operations Dashboard:
   GET /api/dashboard/restore-trend?days=30
   - Query 2.1: Daily restore trend

   GET /api/dashboard/top-databases?days=30&limit=10
   - Query 2.2: Top databases by restore count

   GET /api/dashboard/server-stats?days=30
   - Query 2.3: Server/IP statistics

3. Setup Configuration Dashboard:
   GET /api/dashboard/setup-stats
   - Query 3.1: Setup configuration statistics

   GET /api/dashboard/config-usage
   - Query 3.2: Setup vs actual restore comparison

   GET /api/dashboard/config-effectiveness
   - Query 3.3: Configuration effectiveness analysis

4. Performance Dashboard:
   GET /api/dashboard/hourly-performance?days=7
   - Query 4.1: Performance by hour

   GET /api/dashboard/size-distribution?days=30
   - Query 4.2: Size distribution analysis

5. Alerts Dashboard:
   GET /api/dashboard/alerts
   - Query 5.1: Critical alerts

   GET /api/dashboard/health-check
   - Query 5.2: System health check

6. Detailed Analysis:
   GET /api/dashboard/restore-details?page=1&size=50&days=30
   - Query 6.1 + 6.2: Detailed restore list with pagination

Parameters:
- days: Number of days to look back (default: 30)
- page: Page number for pagination (default: 1)
- size: Page size for pagination (default: 50)
- limit: Limit for top results (default: 10)
*/

-- =============================================
-- 8. DASHBOARD DESIGN RECOMMENDATIONS
-- =============================================

/*
DASHBOARD LAYOUT SUGGESTIONS:

1. OVERVIEW PAGE (Main Dashboard):
   ┌─────────────────────────────────────────────────────────────┐
   │ KPI Cards Row:                                              │
   │ [Total Restores] [Success Rate] [Active Servers] [Configs] │
   └─────────────────────────────────────────────────────────────┘
   ┌─────────────────────────────────────────────────────────────┐
   │ Charts Row:                                                 │
   │ [Daily Trend Chart]           [Success/Failure Pie Chart]  │
   └─────────────────────────────────────────────────────────────┘
   ┌─────────────────────────────────────────────────────────────┐
   │ Tables Row:                                                 │
   │ [Recent Activity Table]       [Top Databases Table]        │
   └─────────────────────────────────────────────────────────────┘

2. RESTORE OPERATIONS PAGE:
   ┌─────────────────────────────────────────────────────────────┐
   │ [30-Day Trend Line Chart]                                   │
   └─────────────────────────────────────────────────────────────┘
   ┌─────────────────────────────────────────────────────────────┐
   │ [Server Performance Table] [Database Rankings Table]       │
   └─────────────────────────────────────────────────────────────┘

3. CONFIGURATION MANAGEMENT PAGE:
   ┌─────────────────────────────────────────────────────────────┐
   │ [Auto vs Manual Config Chart] [Config Effectiveness Chart] │
   └─────────────────────────────────────────────────────────────┘
   ┌─────────────────────────────────────────────────────────────┐
   │ [Unused Configurations Alert Table]                        │
   └─────────────────────────────────────────────────────────────┘

4. PERFORMANCE ANALYSIS PAGE:
   ┌─────────────────────────────────────────────────────────────┐
   │ [Hourly Performance Heatmap]  [Size Distribution Chart]    │
   └─────────────────────────────────────────────────────────────┘

5. ALERTS & MONITORING PAGE:
   ┌─────────────────────────────────────────────────────────────┐
   │ [Critical Alerts Panel]                                     │
   └─────────────────────────────────────────────────────────────┘
   ┌─────────────────────────────────────────────────────────────┐
   │ [System Health Status]        [Recent Failures Table]      │
   └─────────────────────────────────────────────────────────────┘

RECOMMENDED CHART TYPES:
- Line Chart: Daily trends, hourly performance
- Bar Chart: Top databases, server statistics
- Pie Chart: Success/failure ratio, auto/manual config ratio
- Heatmap: Hourly performance, size distribution
- Table: Detailed listings, alerts, configurations
- KPI Cards: Key metrics, counts, percentages

COLOR SCHEME SUGGESTIONS:
- Success: Green (#10B981)
- Failure/Error: Red (#EF4444)
- Warning: Yellow (#F59E0B)
- Info: Blue (#3B82F6)
- Neutral: Gray (#6B7280)

REFRESH INTERVALS:
- Overview Dashboard: Every 5 minutes
- Detailed Analysis: Every 15 minutes
- Alerts: Every 1 minute
- Historical Data: Every hour
*/
