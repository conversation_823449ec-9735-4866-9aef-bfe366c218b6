USE [IT]
GO

/****** Object:  Table [dbo].[ToolRestoreSQLDatabase]    Script Date: 08/08/2025 12:47:34 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ToolRestoreSQLDatabase](
	[ID] [uniqueidentifier] NOT NULL,
	[IP] [nvarchar](50) NULL,
	[DatabaseName] [nvarchar](255) NULL,
	[<PERSON>zeFileZip] [nchar](10) NULL,
	[SizeBAK] [nchar](10) NULL,
	[DateTimeRestore] [smalldatetime] NULL,
	[isSuccess] [nvarchar](50) NULL,
	[LogContent] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO




USE [IT]
GO

/****** Object:  Table [dbo].[SetupToolRestoreSQLDatabase]    Script Date: 08/08/2025 12:47:52 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SetupToolRestoreSQLDatabase](
	[ID] [uniqueidentifier] NOT NULL,
	[IP] [nvarchar](50) NULL,
	[DatabaseName] [nvarchar](max) NULL,
	[ServerSQL] [nvarchar](50) NULL,
	[UserSQL] [nvarchar](50) NULL,
	[PasswordSQL] [nvarchar](50) NULL,
	[Destination_folder] [nvarchar](max) NULL,
	[Source_File] [nvarchar](max) NULL,
	[Folder_Logical] [nvarchar](max) NULL,
	[DateTimeRestore] [smalldatetime] NULL,
	[isAuto] [nchar](10) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO


