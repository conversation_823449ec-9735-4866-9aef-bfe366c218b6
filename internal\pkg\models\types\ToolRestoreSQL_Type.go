package types

type ToolRestoreSQLDatabase struct {
	ID              string `gorm:"column:ID"`
	IP              string `gorm:"column:IP"`
	DatabaseName    string `gorm:"column:DatabaseName"`
	SizeFileZip     string `gorm:"column:SizeFileZip"`
	SizeBAK         string `gorm:"column:<PERSON>zeBAK"`
	DateTimeRestore string `gorm:"column:DateTimeRestore"`
	IsSuccess       string `gorm:"column:isSuccess"`
	LogContent      string `gorm:"column:LogContent"`
}
