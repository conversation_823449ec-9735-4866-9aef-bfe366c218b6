# Dashboard Frontend Guide - Vue 3 + TypeScript + Element Plus

## 📋 Overview

Hướng dẫn chi tiết để xây dựng Dashboard Frontend sử dụng:
- **Vue 3** với Composition API
- **TypeScript** cho type safety
- **Element Plus** cho UI components
- **CSS thuần** cho custom styling
- **Axios** cho API calls

## 🔧 Setup & Installation

### 1. Dependencies
```bash
npm install vue@next typescript
npm install element-plus
npm install @element-plus/icons-vue
npm install axios
npm install @vueuse/core  # Optional: useful utilities
```

### 2. Element Plus Setup
```typescript
// main.ts
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'

const app = createApp(App)

app.use(ElementPlus)

// Register all icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
```

## 📡 API Types & Interfaces

### Core Types
```typescript
// types/dashboard.ts
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// Overview Dashboard Types
export interface OverviewStats {
  totalRestoreOperations: number
  successfulRestores: number
  failedRestores: number
  successRate: number
  totalSetupConfigs: number
  autoConfigs: number
  manualConfigs: number
  uniqueServersWithRestores: number
  uniqueServersWithSetup: number
  uniqueDatabasesRestored: number
  uniqueDatabasesConfigured: number
}

export interface RecentActivity {
  activityType: string
  count: number
  successCount: number
  failureCount: number
}

// Restore Operations Types
export interface DailyTrend {
  date: string                // dd/MM/yyyy format
  restoreDate: string         // ISO date
  totalRestores: number
  successCount: number
  failureCount: number
  dailySuccessRate: number
  avgSizeBAK: number
  avgSizeZip: number
}

export interface TopDatabase {
  databaseName: string
  restoreCount: number
  successCount: number
  failureCount: number
  successRate: number
  avgSizeBAK: number
  maxSizeBAK: number
  lastRestoreTime: string
  hoursSinceLastRestore: number
}

export interface ServerStats {
  ip: string
  totalRestores: number
  successCount: number
  failureCount: number
  successRate: number
  uniqueDbCount: number
  totalRestoredSizeMB: number
  firstRestore: string
  lastRestore: string
}

// Performance Types
export interface HourlyPerformance {
  hourOfDay: number
  hourLabel: string
  restoreCount: number
  successCount: number
  failureCount: number
  avgSizeBAK: number
  timePeriod: 'Business Hours' | 'Evening' | 'Night' | 'Early Morning'
}

export interface SizeDistribution {
  sizeCategory: 'Small (≤100MB)' | 'Medium (100MB-1GB)' | 'Large (1GB-10GB)' | 'Very Large (10GB-50GB)' | 'Huge (>50GB)'
  restoreCount: number
  successCount: number
  successRate: number
  avgSizeMB: number
  minSizeMB: number
  maxSizeMB: number
}

// Alerts & Monitoring Types
export interface Alert {
  alertType: 'RECENT_FAILURES' | 'HIGH_FAILURE_RATE' | 'UNUSED_CONFIGS'
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  title: string
  count: number
  details: string
  alertTime: string
}

export interface SystemHealth {
  checkType: 'SYSTEM_HEALTH'
  restoresLast24h: number
  successfulRestoresLast24h: number
  newConfigsLast24h: number
  activeServersLast7Days: number
  dataQualityIssues: number
  checkTime: string
}

// Detailed Analysis Types
export interface RestoreDetail {
  id: string
  ip: string
  databaseName: string
  sizeFileZip: string
  sizeBAK: string
  dateTimeRestore: string
  status: 'Success' | 'Failed'
  compressionRatio: number
  serverSQL: string
  configType: 'Auto' | 'Manual'
  destinationFolder: string
  logSummary: string
  hoursSinceRestore: number
}

export interface PaginationInfo {
  totalRecords: number
  totalPages: number
  currentPage: number
  pageSize: number
}

export interface PaginatedRestoreDetails {
  data: RestoreDetail[]
  pagination: PaginationInfo
}

// Request Parameters
export interface DashboardQueryParams {
  days?: number
  limit?: number
}

export interface RestoreDetailsParams extends DashboardQueryParams {
  page?: number
  size?: number
  database?: string
  ip?: string
  status?: 'success' | 'failed'
  sort?: string
  order?: 'asc' | 'desc'
}
```

## 🌐 API Service Layer

### Dashboard API Service
```typescript
// services/dashboardApi.ts
import axios, { AxiosResponse } from 'axios'
import type { 
  ApiResponse, 
  OverviewStats, 
  RecentActivity,
  DailyTrend,
  TopDatabase,
  ServerStats,
  HourlyPerformance,
  SizeDistribution,
  Alert,
  SystemHealth,
  PaginatedRestoreDetails,
  DashboardQueryParams,
  RestoreDetailsParams
} from '@/types/dashboard'

const API_BASE_URL = 'http://localhost:8003/api/v1/dashboard'

class DashboardApiService {
  private apiClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  constructor() {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        if (response.data.code !== 200) {
          throw new Error(response.data.message || 'API Error')
        }
        return response
      },
      (error) => {
        console.error('API Error:', error)
        throw error
      }
    )
  }

  // Overview Dashboard APIs
  async getOverview(): Promise<{ stats: OverviewStats; activity: RecentActivity[] }> {
    const response = await this.apiClient.get<ApiResponse<{ stats: OverviewStats; activity: RecentActivity[] }>>('/overview')
    return response.data.data
  }

  async getSummary(days: number = 30): Promise<any> {
    const response = await this.apiClient.get<ApiResponse<any>>(`/summary?days=${days}`)
    return response.data.data
  }

  // Restore Operations APIs
  async getRestoreTrend(params: DashboardQueryParams = {}): Promise<DailyTrend[]> {
    const { days = 30 } = params
    const response = await this.apiClient.get<ApiResponse<DailyTrend[]>>(`/restore-trend?days=${days}`)
    return response.data.data
  }

  async getTopDatabases(params: DashboardQueryParams = {}): Promise<TopDatabase[]> {
    const { days = 30, limit = 10 } = params
    const response = await this.apiClient.get<ApiResponse<TopDatabase[]>>(`/top-databases?days=${days}&limit=${limit}`)
    return response.data.data
  }

  async getServerStats(params: DashboardQueryParams = {}): Promise<ServerStats[]> {
    const { days = 30 } = params
    const response = await this.apiClient.get<ApiResponse<ServerStats[]>>(`/server-stats?days=${days}`)
    return response.data.data
  }

  // Performance APIs
  async getHourlyPerformance(params: DashboardQueryParams = {}): Promise<HourlyPerformance[]> {
    const { days = 7 } = params
    const response = await this.apiClient.get<ApiResponse<HourlyPerformance[]>>(`/hourly-performance?days=${days}`)
    return response.data.data
  }

  async getSizeDistribution(params: DashboardQueryParams = {}): Promise<SizeDistribution[]> {
    const { days = 30 } = params
    const response = await this.apiClient.get<ApiResponse<SizeDistribution[]>>(`/size-distribution?days=${days}`)
    return response.data.data
  }

  // Alerts & Monitoring APIs
  async getAlerts(): Promise<Alert[]> {
    const response = await this.apiClient.get<ApiResponse<Alert[]>>('/alerts')
    return response.data.data
  }

  async getHealthCheck(): Promise<SystemHealth> {
    const response = await this.apiClient.get<ApiResponse<SystemHealth>>('/health-check')
    return response.data.data
  }

  // Detailed Analysis APIs
  async getRestoreDetails(params: RestoreDetailsParams = {}): Promise<PaginatedRestoreDetails> {
    const queryParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value))
      }
    })

    const response = await this.apiClient.get<ApiResponse<PaginatedRestoreDetails>>(`/restore-details?${queryParams}`)
    return response.data.data
  }

  // Utility APIs
  async refreshDashboard(): Promise<void> {
    await this.apiClient.post('/refresh')
  }
}

export const dashboardApi = new DashboardApiService()
export default dashboardApi
```

## 🎣 Vue Composables

### Dashboard Composable
```typescript
// composables/useDashboard.ts
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import dashboardApi from '@/services/dashboardApi'
import type {
  OverviewStats,
  RecentActivity,
  DailyTrend,
  TopDatabase,
  Alert,
  SystemHealth,
  DashboardQueryParams
} from '@/types/dashboard'

export function useDashboard() {
  // Reactive state
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Data state
  const overviewStats = ref<OverviewStats | null>(null)
  const recentActivity = ref<RecentActivity[]>([])
  const dailyTrends = ref<DailyTrend[]>([])
  const topDatabases = ref<TopDatabase[]>([])
  const alerts = ref<Alert[]>([])
  const systemHealth = ref<SystemHealth | null>(null)

  // Auto-refresh
  const refreshInterval = ref<NodeJS.Timeout | null>(null)
  const autoRefreshEnabled = ref(true)

  // Computed
  const hasAlerts = computed(() => alerts.value.length > 0)
  const criticalAlerts = computed(() =>
    alerts.value.filter(alert => alert.priority === 'HIGH')
  )
  const successRate = computed(() =>
    overviewStats.value?.successRate || 0
  )

  // Methods
  const handleError = (err: any, context: string) => {
    console.error(`Error in ${context}:`, err)
    error.value = err.message || `Failed to ${context}`
    ElMessage.error(`Failed to ${context}: ${err.message || 'Unknown error'}`)
  }

  const fetchOverview = async () => {
    try {
      loading.value = true
      const data = await dashboardApi.getOverview()
      overviewStats.value = data.stats
      recentActivity.value = data.activity
    } catch (err) {
      handleError(err, 'fetch overview data')
    } finally {
      loading.value = false
    }
  }

  const fetchDailyTrends = async (params: DashboardQueryParams = {}) => {
    try {
      dailyTrends.value = await dashboardApi.getRestoreTrend(params)
    } catch (err) {
      handleError(err, 'fetch daily trends')
    }
  }

  const fetchTopDatabases = async (params: DashboardQueryParams = {}) => {
    try {
      topDatabases.value = await dashboardApi.getTopDatabases(params)
    } catch (err) {
      handleError(err, 'fetch top databases')
    }
  }

  const fetchAlerts = async () => {
    try {
      alerts.value = await dashboardApi.getAlerts()
    } catch (err) {
      handleError(err, 'fetch alerts')
    }
  }

  const fetchSystemHealth = async () => {
    try {
      systemHealth.value = await dashboardApi.getHealthCheck()
    } catch (err) {
      handleError(err, 'fetch system health')
    }
  }

  const fetchAllData = async (params: DashboardQueryParams = {}) => {
    await Promise.allSettled([
      fetchOverview(),
      fetchDailyTrends(params),
      fetchTopDatabases(params),
      fetchAlerts(),
      fetchSystemHealth()
    ])
  }

  const refreshData = async (params: DashboardQueryParams = {}) => {
    await fetchAllData(params)
    ElMessage.success('Dashboard data refreshed')
  }

  const startAutoRefresh = (intervalMs: number = 5 * 60 * 1000) => { // 5 minutes
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }

    refreshInterval.value = setInterval(() => {
      if (autoRefreshEnabled.value) {
        fetchAllData()
      }
    }, intervalMs)
  }

  const stopAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
  }

  const toggleAutoRefresh = () => {
    autoRefreshEnabled.value = !autoRefreshEnabled.value
    if (autoRefreshEnabled.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  // Lifecycle
  onMounted(() => {
    fetchAllData()
    startAutoRefresh()
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  return {
    // State
    loading: readonly(loading),
    error: readonly(error),
    overviewStats: readonly(overviewStats),
    recentActivity: readonly(recentActivity),
    dailyTrends: readonly(dailyTrends),
    topDatabases: readonly(topDatabases),
    alerts: readonly(alerts),
    systemHealth: readonly(systemHealth),
    autoRefreshEnabled: readonly(autoRefreshEnabled),

    // Computed
    hasAlerts,
    criticalAlerts,
    successRate,

    // Methods
    fetchOverview,
    fetchDailyTrends,
    fetchTopDatabases,
    fetchAlerts,
    fetchSystemHealth,
    fetchAllData,
    refreshData,
    toggleAutoRefresh
  }
}
```

## 🎨 Element Plus Components

### 1. KPI Card Component
```vue
<!-- components/dashboard/KPICard.vue -->
<template>
  <el-card class="kpi-card" :class="cardClass" shadow="hover">
    <div class="kpi-content">
      <div class="kpi-icon">
        <el-icon :size="32" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="kpi-details">
        <div class="kpi-value">{{ formattedValue }}</div>
        <div class="kpi-label">{{ title }}</div>
        <div v-if="trend" class="kpi-trend" :class="trendClass">
          <el-icon size="14">
            <ArrowUp v-if="trend.direction === 'up'" />
            <ArrowDown v-if="trend.direction === 'down'" />
            <Minus v-if="trend.direction === 'stable'" />
          </el-icon>
          <span>{{ Math.abs(trend.percentage) }}%</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

interface Trend {
  direction: 'up' | 'down' | 'stable'
  percentage: number
}

interface Props {
  title: string
  value: number
  icon: string
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  suffix?: string
  trend?: Trend
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  suffix: ''
})

const formattedValue = computed(() => {
  const formatted = new Intl.NumberFormat('vi-VN').format(props.value)
  return `${formatted}${props.suffix}`
})

const cardClass = computed(() => `kpi-card--${props.color}`)

const iconColor = computed(() => {
  const colors = {
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399'
  }
  return colors[props.color]
})

const trendClass = computed(() => {
  if (!props.trend) return ''
  return `kpi-trend--${props.trend.direction}`
})
</script>

<style scoped>
.kpi-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
}

.kpi-content {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.kpi-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.kpi-card--success .kpi-icon {
  background: rgba(103, 194, 58, 0.1);
}

.kpi-card--warning .kpi-icon {
  background: rgba(230, 162, 60, 0.1);
}

.kpi-card--danger .kpi-icon {
  background: rgba(245, 108, 108, 0.1);
}

.kpi-details {
  flex: 1;
}

.kpi-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.kpi-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.kpi-trend--up {
  color: #67C23A;
}

.kpi-trend--down {
  color: #F56C6C;
}

.kpi-trend--stable {
  color: #909399;
}
</style>
```

### 2. Chart Component (using Chart.js)
```vue
<!-- components/dashboard/LineChart.vue -->
<template>
  <div class="chart-container">
    <canvas ref="chartRef"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface Props {
  data: any
  options?: any
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 300
})

const chartRef = ref<HTMLCanvasElement>()
let chartInstance: ChartJS | null = null

const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false,
    }
  },
  scales: {
    x: {
      display: true,
      grid: {
        display: false
      }
    },
    y: {
      display: true,
      beginAtZero: true,
      grid: {
        color: '#E4E7ED'
      }
    }
  },
  interaction: {
    mode: 'nearest' as const,
    axis: 'x' as const,
    intersect: false
  }
}

const createChart = () => {
  if (!chartRef.value) return

  const ctx = chartRef.value.getContext('2d')
  if (!ctx) return

  chartInstance = new ChartJS(ctx, {
    type: 'line',
    data: props.data,
    options: { ...defaultOptions, ...props.options }
  })
}

const updateChart = () => {
  if (chartInstance) {
    chartInstance.data = props.data
    chartInstance.update('none')
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
}

watch(() => props.data, updateChart, { deep: true })

onMounted(async () => {
  await nextTick()
  createChart()
})

onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
.chart-container {
  position: relative;
  height: v-bind(height + 'px');
  width: 100%;
}
</style>
```

### 3. Data Table Component
```vue
<!-- components/dashboard/DataTable.vue -->
<template>
  <el-card class="data-table-card" shadow="never">
    <template #header>
      <div class="table-header">
        <h3>{{ title }}</h3>
        <div class="table-actions">
          <el-button
            v-if="showRefresh"
            :icon="Refresh"
            :loading="loading"
            @click="$emit('refresh')"
            circle
            size="small"
          />
          <el-button
            v-if="showViewAll"
            type="primary"
            size="small"
            @click="$emit('view-all')"
          >
            View All
          </el-button>
        </div>
      </div>
    </template>

    <el-table
      :data="data"
      :loading="loading"
      stripe
      style="width: 100%"
      :empty-text="emptyText"
      @row-click="handleRowClick"
    >
      <slot />
    </el-table>

    <div v-if="showPagination && pagination" class="table-pagination">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.totalRecords"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue'
import type { PaginationInfo } from '@/types/dashboard'

interface Props {
  title: string
  data: any[]
  loading?: boolean
  showRefresh?: boolean
  showViewAll?: boolean
  showPagination?: boolean
  pagination?: PaginationInfo
  emptyText?: string
}

withDefaults(defineProps<Props>(), {
  loading: false,
  showRefresh: true,
  showViewAll: false,
  showPagination: false,
  emptyText: 'No data available'
})

const emit = defineEmits<{
  refresh: []
  'view-all': []
  'row-click': [row: any]
  'size-change': [size: number]
  'current-change': [page: number]
}>()

const handleRowClick = (row: any) => {
  emit('row-click', row)
}

const handleSizeChange = (size: number) => {
  emit('size-change', size)
}

const handleCurrentChange = (page: number) => {
  emit('current-change', page)
}
</script>

<style scoped>
.data-table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
```

### 4. Alert Component
```vue
<!-- components/dashboard/AlertCard.vue -->
<template>
  <el-alert
    :title="alert.title"
    :type="alertType"
    :closable="dismissible"
    show-icon
    @close="$emit('dismiss', alert.alertType)"
  >
    <template #default>
      <div class="alert-content">
        <div class="alert-count">
          <el-tag :type="tagType" size="small">
            {{ alert.count }} {{ alert.count === 1 ? 'item' : 'items' }}
          </el-tag>
        </div>
        <div class="alert-details">{{ alert.details }}</div>
        <div class="alert-time">
          <el-icon><Clock /></el-icon>
          {{ formatTime(alert.alertTime) }}
        </div>
      </div>
    </template>
  </el-alert>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Clock } from '@element-plus/icons-vue'
import type { Alert } from '@/types/dashboard'

interface Props {
  alert: Alert
  dismissible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  dismissible: true
})

defineEmits<{
  dismiss: [alertType: string]
}>()

const alertType = computed(() => {
  switch (props.alert.priority) {
    case 'HIGH': return 'error'
    case 'MEDIUM': return 'warning'
    case 'LOW': return 'info'
    default: return 'info'
  }
})

const tagType = computed(() => {
  switch (props.alert.priority) {
    case 'HIGH': return 'danger'
    case 'MEDIUM': return 'warning'
    case 'LOW': return 'info'
    default: return 'info'
  }
})

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString('vi-VN')
}
</script>

<style scoped>
.alert-content {
  margin-top: 8px;
}

.alert-count {
  margin-bottom: 8px;
}

.alert-details {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}
</style>
```

## 📱 Main Dashboard Views

### Overview Dashboard Page
```vue
<!-- views/dashboard/OverviewDashboard.vue -->
<template>
  <div class="dashboard-overview">
    <!-- Page Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title">Dashboard Overview</h1>
        <div class="header-actions">
          <el-button
            :icon="Refresh"
            :loading="loading"
            @click="refreshData()"
          >
            Refresh
          </el-button>
          <el-select v-model="selectedDays" @change="onDaysChange" style="width: 120px">
            <el-option label="7 days" :value="7" />
            <el-option label="30 days" :value="30" />
            <el-option label="90 days" :value="90" />
          </el-select>
          <el-switch
            v-model="autoRefreshEnabled"
            @change="toggleAutoRefresh"
            active-text="Auto Refresh"
            inactive-text="Manual"
          />
        </div>
      </div>
    </div>

    <!-- Alerts Section -->
    <div v-if="hasAlerts" class="alerts-section">
      <h3>Active Alerts</h3>
      <el-row :gutter="16">
        <el-col
          v-for="alert in alerts"
          :key="alert.alertType"
          :xs="24" :sm="12" :md="8" :lg="6"
        >
          <AlertCard
            :alert="alert"
            @dismiss="dismissAlert"
          />
        </el-col>
      </el-row>
    </div>

    <!-- KPI Cards -->
    <div class="kpi-section">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <KPICard
            title="Total Restores"
            :value="overviewStats?.totalRestoreOperations || 0"
            icon="Database"
            color="primary"
          />
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <KPICard
            title="Success Rate"
            :value="successRate"
            suffix="%"
            icon="SuccessFilled"
            color="success"
          />
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <KPICard
            title="Active Servers"
            :value="overviewStats?.uniqueServersWithRestores || 0"
            icon="Monitor"
            color="info"
          />
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <KPICard
            title="Configurations"
            :value="overviewStats?.totalSetupConfigs || 0"
            icon="Setting"
            color="warning"
          />
        </el-col>
      </el-row>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <el-row :gutter="16">
        <el-col :xs="24" :lg="16">
          <el-card title="Daily Restore Trend" shadow="never">
            <LineChart
              :data="trendChartData"
              :options="trendChartOptions"
              :height="300"
            />
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="8">
          <el-card title="Success vs Failed" shadow="never">
            <div class="success-rate-display">
              <el-progress
                type="circle"
                :percentage="successRate"
                :width="120"
                :stroke-width="8"
                :color="progressColor"
              />
              <div class="success-stats">
                <div class="stat-item">
                  <span class="stat-label">Successful:</span>
                  <span class="stat-value success">{{ overviewStats?.successfulRestores || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Failed:</span>
                  <span class="stat-value failed">{{ overviewStats?.failedRestores || 0 }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Tables Section -->
    <div class="tables-section">
      <el-row :gutter="16">
        <el-col :xs="24" :lg="12">
          <DataTable
            title="Recent Activity"
            :data="recentActivity"
            :loading="loading"
            show-view-all
            @view-all="$router.push('/dashboard/activity')"
          >
            <el-table-column prop="activityType" label="Type" width="150" />
            <el-table-column prop="count" label="Count" width="80" />
            <el-table-column prop="successCount" label="Success" width="80" />
            <el-table-column prop="failureCount" label="Failed" width="80" />
          </DataTable>
        </el-col>
        <el-col :xs="24" :lg="12">
          <DataTable
            title="Top Databases"
            :data="topDatabases"
            :loading="loading"
            show-view-all
            @view-all="$router.push('/dashboard/databases')"
          >
            <el-table-column prop="databaseName" label="Database" />
            <el-table-column prop="restoreCount" label="Count" width="80" />
            <el-table-column prop="successRate" label="Success Rate" width="100">
              <template #default="{ row }">
                <el-tag :type="row.successRate >= 90 ? 'success' : row.successRate >= 70 ? 'warning' : 'danger'">
                  {{ row.successRate.toFixed(1) }}%
                </el-tag>
              </template>
            </el-table-column>
          </DataTable>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { useDashboard } from '@/composables/useDashboard'
import KPICard from '@/components/dashboard/KPICard.vue'
import LineChart from '@/components/dashboard/LineChart.vue'
import DataTable from '@/components/dashboard/DataTable.vue'
import AlertCard from '@/components/dashboard/AlertCard.vue'

const {
  loading,
  overviewStats,
  recentActivity,
  dailyTrends,
  topDatabases,
  alerts,
  hasAlerts,
  successRate,
  autoRefreshEnabled,
  refreshData,
  toggleAutoRefresh,
  fetchAllData
} = useDashboard()

const selectedDays = ref(30)

// Chart data
const trendChartData = computed(() => ({
  labels: dailyTrends.value.map(d => d.date),
  datasets: [
    {
      label: 'Success',
      data: dailyTrends.value.map(d => d.successCount),
      borderColor: '#67C23A',
      backgroundColor: 'rgba(103, 194, 58, 0.1)',
      tension: 0.4,
      fill: true
    },
    {
      label: 'Failed',
      data: dailyTrends.value.map(d => d.failureCount),
      borderColor: '#F56C6C',
      backgroundColor: 'rgba(245, 108, 108, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
}))

const trendChartOptions = {
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      stacked: false
    }
  }
}

const progressColor = computed(() => {
  const rate = successRate.value
  if (rate >= 90) return '#67C23A'
  if (rate >= 70) return '#E6A23C'
  return '#F56C6C'
})

const onDaysChange = () => {
  fetchAllData({ days: selectedDays.value })
}

const dismissAlert = (alertType: string) => {
  // Handle alert dismissal
  console.log('Dismiss alert:', alertType)
}
</script>

<style scoped>
.dashboard-overview {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.alerts-section,
.kpi-section,
.charts-section,
.tables-section {
  margin-bottom: 20px;
}

.alerts-section h3 {
  margin-bottom: 16px;
  color: #303133;
}

.success-rate-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.success-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-weight: 600;
  font-size: 16px;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.failed {
  color: #F56C6C;
}

@media (max-width: 768px) {
  .dashboard-overview {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }
}
</style>
```

## 🛠️ Utilities & Helpers

### Date Formatting Utilities
```typescript
// utils/dateUtils.ts
export const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('vi-VN')
}

export const formatTime = (dateString: string): string => {
  return new Date(dateString).toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

export const getRelativeTime = (dateString: string): string => {
  const now = new Date()
  const date = new Date(dateString)
  const diffMs = now.getTime() - date.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)

  if (diffHours < 1) return 'Just now'
  if (diffHours < 24) return `${diffHours} hours ago`
  if (diffDays < 7) return `${diffDays} days ago`
  return formatDate(dateString)
}
```

### Number Formatting Utilities
```typescript
// utils/numberUtils.ts
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('vi-VN').format(num)
}

export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`
}

export const parseSize = (sizeString: string): number => {
  const match = sizeString.match(/^(\d+(?:\.\d+)?)\s*(MB|GB|TB)?$/i)
  if (!match) return 0

  const value = parseFloat(match[1])
  const unit = (match[2] || 'MB').toUpperCase()

  switch (unit) {
    case 'TB': return value * 1024 * 1024
    case 'GB': return value * 1024
    case 'MB': return value
    default: return value
  }
}
```

### Chart Color Utilities
```typescript
// utils/chartUtils.ts
export const chartColors = {
  primary: '#409EFF',
  success: '#67C23A',
  warning: '#E6A23C',
  danger: '#F56C6C',
  info: '#909399'
}

export const generateChartColors = (count: number): string[] => {
  const baseColors = Object.values(chartColors)
  const colors: string[] = []

  for (let i = 0; i < count; i++) {
    colors.push(baseColors[i % baseColors.length])
  }

  return colors
}

export const createGradient = (ctx: CanvasRenderingContext2D, color: string): CanvasGradient => {
  const gradient = ctx.createLinearGradient(0, 0, 0, 400)
  gradient.addColorStop(0, color)
  gradient.addColorStop(1, color + '20') // Add transparency
  return gradient
}
```

## 📋 Best Practices & Guidelines

### 1. Component Structure
```
src/
├── components/
│   ├── dashboard/
│   │   ├── KPICard.vue
│   │   ├── LineChart.vue
│   │   ├── DataTable.vue
│   │   └── AlertCard.vue
│   └── common/
├── composables/
│   ├── useDashboard.ts
│   └── useCharts.ts
├── services/
│   └── dashboardApi.ts
├── types/
│   └── dashboard.ts
├── utils/
│   ├── dateUtils.ts
│   ├── numberUtils.ts
│   └── chartUtils.ts
└── views/
    └── dashboard/
        ├── OverviewDashboard.vue
        ├── RestoreOperations.vue
        └── PerformanceAnalysis.vue
```

### 2. Error Handling
```typescript
// composables/useErrorHandler.ts
import { ElMessage, ElNotification } from 'element-plus'

export function useErrorHandler() {
  const handleApiError = (error: any, context: string) => {
    console.error(`Error in ${context}:`, error)

    const message = error.response?.data?.message || error.message || 'Unknown error'

    if (error.response?.status >= 500) {
      ElNotification.error({
        title: 'Server Error',
        message: `Failed to ${context}: ${message}`,
        duration: 5000
      })
    } else {
      ElMessage.error(`Failed to ${context}: ${message}`)
    }
  }

  const handleSuccess = (message: string) => {
    ElMessage.success(message)
  }

  return {
    handleApiError,
    handleSuccess
  }
}
```

### 3. Performance Optimization
```typescript
// composables/usePerformance.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function usePerformance() {
  const isVisible = ref(true)

  const handleVisibilityChange = () => {
    isVisible.value = !document.hidden
  }

  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange)
  })

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })

  return {
    isVisible: readonly(isVisible)
  }
}
```

### 4. Responsive Design Guidelines
```css
/* styles/responsive.css */
.dashboard-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Element Plus responsive utilities */
.el-row {
  margin-left: -8px !important;
  margin-right: -8px !important;
}

.el-col {
  padding-left: 8px !important;
  padding-right: 8px !important;
}
```

### 5. Theme Customization
```css
/* styles/dashboard-theme.css */
:root {
  --dashboard-primary: #409EFF;
  --dashboard-success: #67C23A;
  --dashboard-warning: #E6A23C;
  --dashboard-danger: #F56C6C;
  --dashboard-info: #909399;

  --dashboard-bg: #f5f7fa;
  --dashboard-card-bg: #ffffff;
  --dashboard-border: #EBEEF5;
  --dashboard-text-primary: #303133;
  --dashboard-text-regular: #606266;
  --dashboard-text-secondary: #909399;
}

.dashboard-card {
  background: var(--dashboard-card-bg);
  border: 1px solid var(--dashboard-border);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
```

## 🚀 Deployment Checklist

### 1. Environment Configuration
```typescript
// config/environment.ts
export const config = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8003/api/v1',
  refreshInterval: parseInt(import.meta.env.VITE_REFRESH_INTERVAL || '300000'), // 5 minutes
  enableAutoRefresh: import.meta.env.VITE_ENABLE_AUTO_REFRESH === 'true',
  chartAnimations: import.meta.env.VITE_CHART_ANIMATIONS !== 'false'
}
```

### 2. Build Optimization
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'charts': ['chart.js'],
          'dashboard': ['./src/views/dashboard/OverviewDashboard.vue']
        }
      }
    }
  },
  optimizeDeps: {
    include: ['element-plus', 'chart.js']
  }
})
```

### 3. Testing Setup
```typescript
// tests/dashboard.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import OverviewDashboard from '@/views/dashboard/OverviewDashboard.vue'

describe('OverviewDashboard', () => {
  it('renders correctly', () => {
    const wrapper = mount(OverviewDashboard)
    expect(wrapper.find('.dashboard-overview').exists()).toBe(true)
  })

  it('displays KPI cards', () => {
    const wrapper = mount(OverviewDashboard)
    expect(wrapper.findAll('.kpi-card')).toHaveLength(4)
  })
})
```

---

**Lưu ý**: Tài liệu này cung cấp foundation hoàn chỉnh để xây dựng Dashboard Frontend với Vue 3, TypeScript và Element Plus. Customize theo nhu cầu cụ thể của dự án.
