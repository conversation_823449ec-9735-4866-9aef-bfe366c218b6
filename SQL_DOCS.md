-- =============================================
-- SQL SELECT Queries cho Dashboard API Giám sát Restore
-- Chỉ sử dụng SELECT, không tạo mới database objects
-- =============================================

-- 1. API: Lấy thống kê tổng quan (Summary Statistics)
-- Usage: GET /api/dashboard/summary?days=30
-- =============================================
SELECT 
    -- Tổng số restore
    COUNT(*) as TotalRestores,
    
    -- Số lần thành công/thất bại
    SUM(CASE WHEN isSuccess = 1 THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN isSuccess = 0 THEN 1 ELSE 0 END) as FailureCount,
    
    -- Tỷ lệ thành công
    CAST(
        CASE WHEN COUNT(*) > 0 
        THEN (SUM(CASE WHEN isSuccess = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
        ELSE 0 END AS DECIMAL(5,2)
    ) as SuccessRate,
    
    -- Thời gian restore trung bình (estimate based on size: 75MB/s)
    AVG(
        CASE WHEN isSuccess = 1 AND SizeBAK > 0
        THEN SizeBAK / 75
        ELSE NULL END
    ) as AvgRestoreTimeSeconds,
    
    -- Tỷ lệ nén trung bình
    AVG(
        CASE WHEN SizeBAK > 0 AND SizeFileZip > 0 
        THEN ((SizeBAK - SizeFileZip) * 100.0 / SizeBAK)
        ELSE NULL END
    ) as AvgCompressionRatio,
    
    -- Tổng dung lượng đã restore
    SUM(CASE WHEN isSuccess = 1 THEN SizeBAK ELSE 0 END) as TotalRestoredSizeMB,
    
    -- Tổng dung lượng file zip
    SUM(CASE WHEN isSuccess = 1 THEN SizeFileZip ELSE 0 END) as TotalZipSizeMB
    
FROM [IT].[dbo].[ToolRestoreSQLDatabase]
WHERE DateTimeRestore >= DATEADD(DAY, -30, GETDATE()); -- Thay 30 bằng parameter từ API

-- =============================================
-- 2. API: Xu hướng restore theo ngày
-- Usage: GET /api/dashboard/daily-trend?days=30
-- =============================================
WITH DateRange AS (
    SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -30, GETDATE())) AS DATE) as RestoreDate
    FROM master..spt_values 
    WHERE type = 'p' AND number <= 29  -- 30 days: 0-29
)
SELECT 
    FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
    ISNULL(SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END), 0) as SuccessCount,
    ISNULL(SUM(CASE WHEN t.isSuccess = 0 THEN 1 ELSE 0 END), 0) as FailureCount,
    ISNULL(COUNT(t.ID), 0) as TotalCount,
    ISNULL(AVG(CAST(t.SizeBAK AS FLOAT)), 0) as AvgSizeBAK,
    ISNULL(AVG(CAST(t.SizeFileZip AS FLOAT)), 0) as AvgSizeZip
FROM DateRange dr
LEFT JOIN [IT].[dbo].[ToolRestoreSQLDatabase] t 
    ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
GROUP BY dr.RestoreDate
ORDER BY dr.RestoreDate;

-- =============================================
-- 3. API: Top Database được restore nhiều nhất
-- Usage: GET /api/dashboard/top-databases?days=30&limit=10
-- =============================================
SELECT TOP 10
    t.DatabaseName,
    COUNT(*) as RestoreCount,
    SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN t.isSuccess = 0 THEN 1 ELSE 0 END) as FailureCount,
    
    -- Tỷ lệ thành công
    CAST(
        CASE WHEN COUNT(*) > 0 
        THEN (SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
        ELSE 0 END AS DECIMAL(5,2)
    ) as SuccessRate,
    
    -- Thời gian restore trung bình (estimate: 75MB/s)
    AVG(
        CASE WHEN t.isSuccess = 1 AND t.SizeBAK > 0
        THEN t.SizeBAK / 75
        ELSE NULL END
    ) as AvgRestoreTimeSeconds,
    
    -- Dung lượng trung bình
    AVG(CAST(t.SizeBAK AS FLOAT)) as AvgSizeBAK,
    AVG(CAST(t.SizeFileZip AS FLOAT)) as AvgSizeZip,
    
    -- Tỷ lệ nén trung bình
    AVG(
        CASE WHEN t.SizeBAK > 0 AND t.SizeFileZip > 0 
        THEN ((t.SizeBAK - t.SizeFileZip) * 100.0 / t.SizeBAK)
        ELSE NULL END
    ) as AvgCompressionRatio
     
FROM [IT].[dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE()) -- Thay 30 bằng parameter
GROUP BY t.DatabaseName
ORDER BY RestoreCount DESC;

-- =============================================
-- 4. API: Hiệu suất theo giờ trong ngày
-- Usage: GET /api/dashboard/hourly-performance?days=7
-- =============================================
WITH Hours AS (
    SELECT number as HourOfDay
    FROM master..spt_values 
    WHERE type = 'p' AND number BETWEEN 0 AND 23
)
SELECT 
    h.HourOfDay,
    FORMAT(h.HourOfDay, '00') + ':00' as HourLabel,
    
    ISNULL(COUNT(t.ID), 0) as RestoreCount,
    ISNULL(SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END), 0) as SuccessCount,
    ISNULL(SUM(CASE WHEN t.isSuccess = 0 THEN 1 ELSE 0 END), 0) as FailureCount,
    
    -- Thời gian restore trung bình (estimate: slower during peak hours)
    ISNULL(AVG(
        CASE WHEN t.isSuccess = 1 AND t.SizeBAK > 0
        THEN CASE 
            WHEN h.HourOfDay BETWEEN 8 AND 17 THEN t.SizeBAK / 50  -- Peak hours slower
            ELSE t.SizeBAK / 100  -- Off-peak faster
        END
        ELSE NULL END
    ), 0) as AvgRestoreTimeSeconds,
    
    -- Dung lượng trung bình
    ISNULL(AVG(CAST(t.SizeBAK AS FLOAT)), 0) as AvgSizeBAK,
    
    -- Load period classification
    CASE 
        WHEN h.HourOfDay BETWEEN 8 AND 17 THEN 'Peak Hours'
        WHEN h.HourOfDay BETWEEN 18 AND 22 THEN 'Evening'
        ELSE 'Off-Peak'
    END as LoadPeriod
    
FROM Hours h
LEFT JOIN [IT].[dbo].[ToolRestoreSQLDatabase] t 
    ON DATEPART(HOUR, t.DateTimeRestore) = h.HourOfDay
    AND t.DateTimeRestore >= DATEADD(DAY, -7, GETDATE()) -- Thay 7 bằng parameter
GROUP BY h.HourOfDay
ORDER BY h.HourOfDay;

-- =============================================
-- 5. API: Chi tiết danh sách restore với phân trang
-- Usage: GET /api/dashboard/restore-details?page=1&size=20&database=Production&sort=DateTimeRestore&order=desc
-- =============================================

-- 5a. Query cho pagination với filtering
SELECT 
    t.ID,
    t.IP,
    t.DatabaseName,
    t.SizeFileZip,
    t.SizeBAK,
    t.DateTimeRestore,
    t.isSuccess,
    
    -- Tính tỷ lệ nén
    CASE WHEN t.SizeBAK > 0 AND t.SizeFileZip > 0 
        THEN CAST(((t.SizeBAK - t.SizeFileZip) * 100.0 / t.SizeBAK) AS DECIMAL(5,2))
        ELSE 0 
    END as CompressionRatio,
    
    -- Ước tính thời gian restore
    CASE WHEN t.SizeBAK > 0
        THEN CAST((t.SizeBAK / 75.0) AS INT)  -- 75MB/s average
        ELSE 0
    END as EstimatedRestoreTimeSeconds,
    
    -- Performance category
    CASE 
        WHEN t.SizeBAK <= 1000 THEN 'Small'
        WHEN t.SizeBAK <= 5000 THEN 'Medium' 
        WHEN t.SizeBAK <= 15000 THEN 'Large'
        ELSE 'Very Large'
    END as SizeCategory,
    
    -- Log content summary (first 100 chars)
    CASE 
        WHEN LEN(t.LogContent) > 100 THEN LEFT(t.LogContent, 100) + '...'
        ELSE t.LogContent
    END as LogSummary
    
FROM [IT].[dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
    -- Add filters from API parameters:
    -- AND t.DatabaseName LIKE '%' + @DatabaseName + '%'  -- if database filter provided
    -- AND t.IP = @IP  -- if IP filter provided
    -- AND t.isSuccess = @IsSuccess  -- if success filter provided
ORDER BY t.DateTimeRestore DESC
OFFSET 0 ROWS FETCH NEXT 20 ROWS ONLY; -- Replace with (page-1)*size and size from API

-- 5b. Count query for total records (for pagination info)
SELECT COUNT(*) as TotalRecords
FROM [IT].[dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE());
    -- Add same filters as above

-- =============================================
-- 6. API: Thống kê theo Server/IP
-- Usage: GET /api/dashboard/server-stats?days=30
-- =============================================
SELECT 
    t.IP,
    
    -- Thống kê restore
    COUNT(*) as TotalRestores,
    SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN t.isSuccess = 0 THEN 1 ELSE 0 END) as FailureCount,
    
    -- Tỷ lệ thành công
    CAST(
        CASE WHEN COUNT(*) > 0 
        THEN (SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
        ELSE 0 END AS DECIMAL(5,2)
    ) as SuccessRate,
    
    -- Thống kê dung lượng
    SUM(CASE WHEN t.isSuccess = 1 THEN t.SizeBAK ELSE 0 END) as TotalRestoredSizeMB,
    AVG(CASE WHEN t.isSuccess = 1 THEN CAST(t.SizeBAK AS FLOAT) ELSE NULL END) as AvgSizeBAK,
    
    -- Số database unique trên server này
    COUNT(DISTINCT t.DatabaseName) as UniqueDbCount,
    
    -- Thời gian restore gần nhất
    MAX(t.DateTimeRestore) as LastRestoreTime
        
FROM [IT].[dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE()) -- Thay 30 bằng parameter
GROUP BY t.IP
ORDER BY TotalRestores DESC;

-- =============================================
-- 7. API: Database được restore nhiều nhất trên từng server
-- Usage: GET /api/dashboard/server-top-databases?days=30
-- =============================================
SELECT 
    t.IP,
    t.DatabaseName,
    COUNT(*) as RestoreCount,
    SUM(CASE WHEN t.isSuccess = 1 THEN 1 ELSE 0 END) as SuccessCount,
    AVG(CASE WHEN t.isSuccess = 1 THEN CAST(t.SizeBAK AS FLOAT) ELSE NULL END) as AvgSizeBAK,
    MAX(t.DateTimeRestore) as LastRestoreTime,
    
    -- Ranking trong server
    ROW_NUMBER() OVER (PARTITION BY t.IP ORDER BY COUNT(*) DESC) as RankInServer
    
FROM [IT].[dbo].[ToolRestoreSQLDatabase] t
WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE()) -- Thay 30 bằng parameter
GROUP BY t.IP, t.DatabaseName
ORDER BY t.IP, RestoreCount DESC;

-- =============================================
-- 8. API: Cảnh báo và bất thường
-- Usage: GET /api/dashboard/alerts
-- =============================================

-- 8a. Restore failures trong 24h qua
SELECT 
    'FAILURE_ALERT' as AlertType,
    'HIGH' as Priority,
    'Restore thất bại trong 24h qua' as AlertTitle,
    COUNT(*) as Count,
    STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
    GETDATE() as AlertTime
FROM [IT].[dbo].[ToolRestoreSQLDatabase] 
WHERE isSuccess = 0 
    AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
HAVING COUNT(*) > 0;

-- 8b. Database có tỷ lệ thất bại cao (>20%) trong 7 ngày qua
SELECT 
    'HIGH_FAILURE_RATE' as AlertType,
    'MEDIUM' as Priority,
    'Database có tỷ lệ thất bại cao (>20%)' as AlertTitle,
    COUNT(*) as Count,
    STRING_AGG(DatabaseName, ', ') as Details,
    GETDATE() as AlertTime
FROM (
    SELECT 
        DatabaseName,
        COUNT(*) as TotalRestores,
        SUM(CASE WHEN isSuccess = 0 THEN 1 ELSE 0 END) as FailureCount,
        CAST((SUM(CASE WHEN isSuccess = 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS DECIMAL(5,2)) as FailureRate
    FROM [IT].[dbo].[ToolRestoreSQLDatabase]
    WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
    GROUP BY DatabaseName
    HAVING COUNT(*) >= 5 AND (SUM(CASE WHEN isSuccess = 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 20
) HighFailureDb
HAVING COUNT(*) > 0;

-- 8c. File size bất thường (quá lớn >50GB)
SELECT 
    'LARGE_FILE_ALERT' as AlertType,
    'LOW' as Priority,
    'File restore có dung lượng lớn bất thường (>50GB)' as AlertTitle,
    COUNT(*) as Count,
    STRING_AGG(DatabaseName + ' (' + CAST(SizeBAK AS VARCHAR) + 'MB)', ', ') as Details,
    GETDATE() as AlertTime
FROM [IT].[dbo].[ToolRestoreSQLDatabase]
WHERE SizeBAK > 50000  -- >50GB in MB
    AND DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
HAVING COUNT(*) > 0;

-- =============================================
-- 9. API: Thống kê theo khoảng thời gian tùy chọn
-- Usage: GET /api/dashboard/custom-period?startDate=2024-08-01&endDate=2024-08-31
-- =============================================
SELECT 
    COUNT(*) as TotalRestores,
    SUM(CASE WHEN isSuccess = 1 THEN 1 ELSE 0 END) as SuccessCount,
    SUM(CASE WHEN isSuccess = 0 THEN 1 ELSE 0 END) as FailureCount,
    
    -- Breakdown by week
    DATEPART(WEEK, DateTimeRestore) as WeekNumber,
    DATENAME(WEEK, DateTimeRestore) as WeekName,
    
    -- Average sizes
    AVG(CAST(SizeBAK AS FLOAT)) as AvgSizeBAK,
    AVG(CAST(SizeFileZip AS FLOAT)) as AvgSizeZip,
    
    -- Performance metrics
    AVG(CASE WHEN SizeBAK > 0 THEN SizeBAK / 75.0 ELSE NULL END) as AvgEstimatedTimeSeconds
    
FROM [IT].[dbo].[ToolRestoreSQLDatabase]
WHERE DateTimeRestore >= '2024-08-01'  -- Replace with @StartDate parameter
    AND DateTimeRestore <= '2024-08-31'  -- Replace with @EndDate parameter
GROUP BY DATEPART(WEEK, DateTimeRestore), DATENAME(WEEK, DateTimeRestore)
ORDER BY WeekNumber;

-- =============================================
-- 10. API: So sánh hiệu suất các database
-- Usage: GET /api/dashboard/database-comparison?days=30
-- =============================================
SELECT 
    DatabaseName,
    COUNT(*) as RestoreCount,
    
    -- Success metrics
    SUM(CASE WHEN isSuccess = 1 THEN 1 ELSE 0 END) as SuccessCount,
    CAST((SUM(CASE WHEN isSuccess = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS DECIMAL(5,2)) as SuccessRate,
    
    -- Size metrics
    MIN(SizeBAK) as MinSize,
    MAX(SizeBAK) as MaxSize,
    AVG(CAST(SizeBAK AS FLOAT)) as AvgSize,
    
    -- Compression metrics
    AVG(CASE WHEN SizeBAK > 0 AND SizeFileZip > 0 
        THEN ((SizeBAK - SizeFileZip) * 100.0 / SizeBAK)
        ELSE NULL END) as AvgCompressionRatio,
        
    -- Time metrics  
    AVG(CASE WHEN SizeBAK > 0 THEN SizeBAK / 75.0 ELSE NULL END) as AvgEstimatedTimeSeconds,
    
    -- Frequency analysis
    COUNT(*) * 1.0 / DATEDIFF(DAY, MIN(DateTimeRestore), MAX(DateTimeRestore) + 1) as AvgRestoresPerDay,
    
    -- Recent activity
    MAX(DateTimeRestore) as LastRestore,
    DATEDIFF(HOUR, MAX(DateTimeRestore), GETDATE()) as HoursSinceLastRestore
    
FROM [IT].[dbo].[ToolRestoreSQLDatabase]
WHERE DateTimeRestore >= DATEADD(DAY, -30, GETDATE()) -- Thay 30 bằng parameter
GROUP BY DatabaseName
HAVING COUNT(*) >= 3  -- Only databases with at least 3 restores
ORDER BY RestoreCount DESC;

-- =============================================
-- Sample API Implementation Notes:
-- =============================================

/*
API Endpoints tương ứng:

1. GET /api/dashboard/summary?days=30
   - Query #1: Summary statistics

2. GET /api/dashboard/daily-trend?days=30  
   - Query #2: Daily trend data

3. GET /api/dashboard/top-databases?days=30&limit=10
   - Query #3: Top databases

4. GET /api/dashboard/hourly-performance?days=7
   - Query #4: Hourly performance

5. GET /api/dashboard/restore-details?page=1&size=20&database=&ip=&success=&sort=DateTimeRestore&order=desc
   - Query #5a + #5b: Paginated details with filters

6. GET /api/dashboard/server-stats?days=30
   - Query #6: Server statistics

7. GET /api/dashboard/alerts
   - Query #8a, #8b, #8c: Different alert types

8. GET /api/dashboard/custom-period?startDate=2024-08-01&endDate=2024-08-31
   - Query #9: Custom date range analysis

9. GET /api/dashboard/database-comparison?days=30
   - Query #10: Database performance comparison

Parameters cần thay thế trong code:
- @Days -> days parameter từ API
- @PageSize, @PageNumber -> pagination parameters  
- @DatabaseName, @IP, @IsSuccess -> filter parameters
- @StartDate, @EndDate -> date range parameters
- @SortColumn, @SortDirection -> sorting parameters
*/