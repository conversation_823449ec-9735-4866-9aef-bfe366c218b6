package services

import (
	"context"
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type SetupToolRestoreSQLServices struct {
	*BaseService
}

var SetupToolRestore = &SetupToolRestoreSQLServices{}


func (s *SetupToolRestoreSQLServices) GetSetupToolRestore(ctx context.Context) ([]types.SetupToolRestoreSQL, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}
	var statistics []types.SetupToolRestoreSQL
	query := `
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	`
	if err := db.Raw(query).Scan(&statistics).Error; err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	return statistics, nil
}

func (s *SetupToolRestoreSQLServices) InsertSetupToolRestore(req *types.SetupToolRestoreSQL) (string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var ID string
	query := `
		INSERT INTO SetupToolRestoreSQLDatabase
		(
			ID,
			IP,
			DatabaseName,
			DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		)
			OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
			VALUES (NEWID(),?,?, GetDate(),?,?,?,?,?,?,?);
		`
		if err := tx.Raw(
		query,
		req.IP,
		req.DatabaseName,
		req.ServerSQL,
		req.UserSQL,
		req.PasswordSQL,
		req.Destination_folder,
		req.Source_File,
		req.Folder_Logical,
		req.IsAuto,
		).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}