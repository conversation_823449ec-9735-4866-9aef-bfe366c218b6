package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type SetupToolRestoreSQLController struct {
	*BaseController
}

var SetupToolRestoreSQL = &SetupToolRestoreSQLController{}



func (c *SetupToolRestoreSQLController) GetSetupToolRestoreSQL(ctx *gin.Context) {
	result, err := services.SetupToolRestore.GetSetupToolRestore(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *SetupToolRestoreSQLController) InsertSetupRestoreSQLDatabase(ctx *gin.Context) {
	var requestParams *types.SetupToolRestoreSQL
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.SetupToolRestore.InsertSetupToolRestore(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}


