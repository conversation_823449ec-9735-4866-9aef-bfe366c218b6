[GIN-debug] GET    /api/v1/ping              --> web-api/internal/api/controllers.(*CommonController).Ping-fm (4 handlers)
[GIN-debug] GET    /api/v1/ping-sql          --> web-api/internal/api/controllers.(*CommonController).PingSQL-fm (4 handlers)
[GIN-debug] POST   /api/v1/login             --> web-api/internal/api/controllers.(*CommonController).Login-fm (4 handlers)
[GIN-debug] GET    /api/v1/GetRestoreSQLDatabase --> web-api/internal/api/controllers.(*ToolRestoreServicesController).GetRestoreSQLDatabase-fm (4 handlers)
[GIN-debug] POST   /api/v1/InsertRestoreSQLDatabase --> web-api/internal/api/controllers.(*ToolRestoreServicesController).InsertRestoreSQLDatabase-fm (4 handlers)
[GIN-debug] GET    /api/v1/GetSetupToolRestoreSQL --> web-api/internal/api/controllers.(*SetupToolRestoreSQLController).GetSetupToolRestoreSQL-fm (4 handlers)
[GIN-debug] POST   /api/v1/InsertSetupRestoreSQLDatabase --> web-api/internal/api/controllers.(*SetupToolRestoreSQLController).InsertSetupRestoreSQLDatabase-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/overview --> web-api/internal/api/controllers.(*DashboardController).GetOverview-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/summary --> web-api/internal/api/controllers.(*DashboardController).GetDashboardSummary-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/restore-trend --> web-api/internal/api/controllers.(*DashboardController).GetRestoreTrend-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/top-databases --> web-api/internal/api/controllers.(*DashboardController).GetTopDatabases-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/server-stats --> web-api/internal/api/controllers.(*DashboardController).GetServerStats-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/setup-stats --> web-api/internal/api/controllers.(*DashboardController).GetSetupStats-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/config-usage --> web-api/internal/api/controllers.(*DashboardController).GetConfigUsage-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/config-effectiveness --> web-api/internal/api/controllers.(*DashboardController).GetConfigEffectiveness-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/hourly-performance --> web-api/internal/api/controllers.(*DashboardController).GetHourlyPerformance-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/size-distribution --> web-api/internal/api/controllers.(*DashboardController).GetSizeDistribution-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/alerts  --> web-api/internal/api/controllers.(*DashboardController).GetAlerts-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/health-check --> web-api/internal/api/controllers.(*DashboardController).GetHealthCheck-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/restore-details --> web-api/internal/api/controllers.(*DashboardController).GetRestoreDetails-fm (4 handlers)
[GIN-debug] POST   /api/v1/dashboard/refresh --> web-api/internal/api/controllers.(*DashboardController).RefreshDashboard-fm (4 handlers)
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8003
::1 - - [08/Aug/2025:13:26:47 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 15.3298ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:47 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 17.3243ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:47 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 20.4391ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:47 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 19.4009ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:47 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 31.0415ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:49 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 10.5983ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:49 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 10.5983ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:49 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 11.6131ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:49 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 13.5475ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:49 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 26.9593ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:50 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 10.4145ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:50 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 11.5093ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:50 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 12.0149ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:50 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 12.0149ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:50 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 25.7447ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:52 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 14.9522ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:52 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 14.9493ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:52 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 18.7322ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:52 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 21.0066ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:52 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 29.7529ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:58 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 21.4842ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:58 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 20.4671ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:58 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 28.7384ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:58 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 30.2615ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:58 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 42.0376ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "OPTIONS /api/v1/GetSetupToolRestoreSQL HTTP/1.1 204 0s " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "GET /api/v1/GetSetupToolRestoreSQL HTTP/1.1 200 15.5005ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 22.0027ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 24.0712ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 27.0006ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 35.2204ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:26:59 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 35.4454ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:07 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 16.207ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:07 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 21.1951ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:07 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 21.7811ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:07 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 21.7811ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:07 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 45.4721ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:14 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 13.4456ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:14 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 14.4614ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:14 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 14.4614ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:14 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 18.0201ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:14 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 29.4173ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 13.84ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 14.3446ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 16.4594ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 16.4594ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 27.2656ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 16.6018ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 17.1071ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 19.5149ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 21.2489ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:15 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 28.2463ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:26 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 18.9084ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:26 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 19.4172ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:26 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 22.1627ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:26 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 23.9392ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:27 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 39.5176ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:38 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 16.5709ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:38 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 16.5709ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:38 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 23.3485ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:38 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 23.3485ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:38 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 36.4832ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:39 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 15.5675ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:39 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 16.103ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:39 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 17.154ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:39 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 17.6765ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:39 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 29.5587ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:52 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 17.2616ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:52 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 19.844ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:52 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 19.844ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:52 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 19.844ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:52 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 32.9059ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:56 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 16.0284ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:56 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 17.0828ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:56 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 16.0142ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:56 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 16.5624ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:27:56 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 27.451ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:01 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 12.4546ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:01 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 12.4546ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:01 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 14.249ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:01 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 15.3139ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:01 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 23.9776ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:07 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 12.0666ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:07 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 12.2935ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:07 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 15.1057ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:07 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 17.2903ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:07 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 25.5709ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:19 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 19.5259ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:19 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 22.5046ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:19 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 23.0501ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:19 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 24.6051ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:19 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 40.6055ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:41 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 18.9112ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:41 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 19.8516ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:41 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 22.9881ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:41 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 43.3407ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:41 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 46.7382ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:51 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 40.132ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:51 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 39.6148ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:51 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 40.132ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:51 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 40.6398ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:51 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 50.853ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:53 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 14.6826ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:53 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 18.1409ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:53 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 19.2161ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:53 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 22.2807ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:28:53 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 32.6915ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:01 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 16.1907ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:01 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 16.1622ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:01 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 24.3688ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:01 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 28.0373ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:01 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 42.2619ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:34 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 13.9059ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:34 +0700] "GET /api/v1/dashboard/top-databases?days=30&limit=10 HTTP/1.1 200 14.4304ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:34 +0700] "GET /api/v1/dashboard/restore-trend?days=30 HTTP/1.1 200 18.8899ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:34 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 18.3654ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:34 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 24.7352ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:54 +0700] "GET /api/v1/dashboard/restore-trend?days=7 HTTP/1.1 200 19.3795ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:54 +0700] "GET /api/v1/dashboard/top-databases?days=7&limit=10 HTTP/1.1 200 21.7536ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:54 +0700] "GET /api/v1/dashboard/health-check HTTP/1.1 200 21.0764ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:54 +0700] "GET /api/v1/dashboard/alerts HTTP/1.1 500 24.3635ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
::1 - - [08/Aug/2025:13:29:54 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 35.4945ms " " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36" " "
