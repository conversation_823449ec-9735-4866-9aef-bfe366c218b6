[GIN-debug] GET    /api/v1/ping              --> web-api/internal/api/controllers.(*CommonController).Ping-fm (4 handlers)
[GIN-debug] GET    /api/v1/ping-sql          --> web-api/internal/api/controllers.(*CommonController).PingSQL-fm (4 handlers)
[GIN-debug] POST   /api/v1/login             --> web-api/internal/api/controllers.(*CommonController).Login-fm (4 handlers)
[GIN-debug] GET    /api/v1/GetRestoreSQLDatabase --> web-api/internal/api/controllers.(*ToolRestoreServicesController).GetRestoreSQLDatabase-fm (4 handlers)
[GIN-debug] POST   /api/v1/InsertRestoreSQLDatabase --> web-api/internal/api/controllers.(*ToolRestoreServicesController).InsertRestoreSQLDatabase-fm (4 handlers)
[GIN-debug] GET    /api/v1/GetSetupToolRestoreSQL --> web-api/internal/api/controllers.(*SetupToolRestoreSQLController).GetSetupToolRestoreSQL-fm (4 handlers)
[GIN-debug] POST   /api/v1/InsertSetupRestoreSQLDatabase --> web-api/internal/api/controllers.(*SetupToolRestoreSQLController).InsertSetupRestoreSQLDatabase-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/overview --> web-api/internal/api/controllers.(*DashboardController).GetOverview-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/summary --> web-api/internal/api/controllers.(*DashboardController).GetDashboardSummary-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/restore-trend --> web-api/internal/api/controllers.(*DashboardController).GetRestoreTrend-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/top-databases --> web-api/internal/api/controllers.(*DashboardController).GetTopDatabases-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/server-stats --> web-api/internal/api/controllers.(*DashboardController).GetServerStats-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/setup-stats --> web-api/internal/api/controllers.(*DashboardController).GetSetupStats-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/config-usage --> web-api/internal/api/controllers.(*DashboardController).GetConfigUsage-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/config-effectiveness --> web-api/internal/api/controllers.(*DashboardController).GetConfigEffectiveness-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/hourly-performance --> web-api/internal/api/controllers.(*DashboardController).GetHourlyPerformance-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/size-distribution --> web-api/internal/api/controllers.(*DashboardController).GetSizeDistribution-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/alerts  --> web-api/internal/api/controllers.(*DashboardController).GetAlerts-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/health-check --> web-api/internal/api/controllers.(*DashboardController).GetHealthCheck-fm (4 handlers)
[GIN-debug] GET    /api/v1/dashboard/restore-details --> web-api/internal/api/controllers.(*DashboardController).GetRestoreDetails-fm (4 handlers)
[GIN-debug] POST   /api/v1/dashboard/refresh --> web-api/internal/api/controllers.(*DashboardController).RefreshDashboard-fm (4 handlers)
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8003
::1 - - [08/Aug/2025:13:01:40 +0700] "GET /api/v1/dashboard HTTP/1.1 404 0s " " PostmanRuntime/7.44.1" " "
::1 - - [08/Aug/2025:13:01:54 +0700] "GET /api/v1/overview HTTP/1.1 404 0s " " PostmanRuntime/7.44.1" " "
::1 - - [08/Aug/2025:13:02:17 +0700] "GET /api/v1/dashboard/overview HTTP/1.1 200 105.146ms " " PostmanRuntime/7.44.1" " "
