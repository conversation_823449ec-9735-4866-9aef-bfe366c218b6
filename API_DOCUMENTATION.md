# API Documentation - Tool Restore Database SQL

## Base URL
```
http://localhost:8003/api/v1
```

## Common Response Format

Tất cả API responses đều có cấu trúc chung:

```typescript
interface CommonResponse<T> {
  code: number;      // HTTP status code
  data: T | null;    // Response data
  message: string;   // Response message
}
```

### Success Response
```json
{
  "code": 200,
  "data": {...},
  "message": "success"
}
```

### Error Response
```json
{
  "code": 400,
  "data": null,
  "message": "Error message"
}
```

## Authentication

### POST /login
Đăng nhập vào hệ thống

**Request Body:**
```typescript
interface LoginRequest {
  userID: string;    // Required - Tên đăng nhập
  password: string;  // Required - Mật khẩu
}
```

**Example Request:**
```json
{
  "userID": "51295",
  "password": "your_password"
}
```

**Success Response:**
```typescript
interface User {
  userID: string;
  userName: string;
  password: string;  // Empty trong response
  role: string;
  token?: string;    // Token để authenticate các request khác
}
```

**Example Response:**
```json
{
  "code": 200,
  "data": {
    "userID": "51295",
    "userName": "John Doe",
    "password": "",
    "role": "admin",
    "token": "token_51295"
  },
  "message": "success"
}
```

**Error Responses:**
- `401`: "Account does not exist or has been locked"
- `401`: "Password is incorrect"
- `400`: "Invalid input format"

## Health Check APIs

### GET /ping
Kiểm tra trạng thái server

**Response:**
```json
{
  "code": 200,
  "data": null,
  "message": "success"
}
```

### GET /ping-sql
Kiểm tra kết nối database

**Response:**
```json
{
  "code": 200,
  "data": null,
  "message": "success"
}
```

## Tool Restore SQL Database APIs

### GET /GetRestoreSQLDatabase
Lấy danh sách các lần restore database

**Response Data:**
```typescript
interface ToolRestoreSQLDatabase {
  ID: string;              // UUID của record
  IP: string;              // IP address
  DatabaseName: string;    // Tên database
  SizeFileZip: string;     // Kích thước file zip
  SizeBAK: string;         // Kích thước file BAK
  DateTimeRestore: string; // Ngày restore (format: YYYY-MM-DD)
  IsSuccess: string;       // Trạng thái thành công ("1" hoặc "0")
  LogContent: string;      // Nội dung log
}
```

**Example Response:**
```json
{
  "code": 200,
  "data": [
    {
      "ID": "123e4567-e89b-12d3-a456-************",
      "IP": "*************",
      "DatabaseName": "TestDB",
      "SizeFileZip": "100MB",
      "SizeBAK": "250MB",
      "DateTimeRestore": "2025-08-07",
      "IsSuccess": "1",
      "LogContent": "Restore completed successfully"
    }
  ],
  "message": "success"
}
```

### POST /InsertRestoreSQLDatabase
Thêm record restore database mới

**Request Body:**
```typescript
interface ToolRestoreSQLDatabase {
  IP: string;              // Required - IP address
  DatabaseName: string;    // Required - Tên database
  SizeFileZip: string;     // Required - Kích thước file zip
  SizeBAK: string;         // Required - Kích thước file BAK
  IsSuccess: string;       // Required - Trạng thái ("1" hoặc "0")
  LogContent: string;      // Required - Nội dung log
}
```

**Example Request:**
```json
{
  "IP": "*************",
  "DatabaseName": "TestDB",
  "SizeFileZip": "100MB",
  "SizeBAK": "250MB",
  "IsSuccess": "1",
  "LogContent": "Restore completed successfully"
}
```

**Success Response:**
```json
{
  "code": 200,
  "data": "123e4567-e89b-12d3-a456-************",  // ID của record mới tạo
  "message": "success"
}
```

**Error Responses:**
- `400`: "Invalid form data"
- `500`: Database connection hoặc query error

## Setup Tool Restore SQL APIs

### GET /GetSetupToolRestoreSQL
Lấy danh sách cấu hình restore database

**Response Data:**
```typescript
interface SetupToolRestoreSQL {
  ID: string;                 // UUID của record
  IP: string;                 // IP address
  DatabaseName: string;       // Tên database
  ServerSQL: string;          // SQL Server name
  UserSQL: string;            // SQL username
  PasswordSQL: string;        // SQL password
  Destination_folder: string; // Thư mục đích
  Source_File: string;        // File nguồn
  Folder_Logical: string;     // Thư mục logical
  DateTimeRestore: string;    // Ngày tạo setup (format: YYYY-MM-DD)
  IsAuto: string;            // Tự động ("1" hoặc "0")
}
```

**Example Response:**
```json
{
  "code": 200,
  "data": [
    {
      "ID": "123e4567-e89b-12d3-a456-************",
      "IP": "*************",
      "DatabaseName": "TestDB",
      "ServerSQL": "SQLSERVER01",
      "UserSQL": "sa",
      "PasswordSQL": "password123",
      "Destination_folder": "C:\\Backup\\",
      "Source_File": "TestDB.bak",
      "Folder_Logical": "C:\\Data\\",
      "DateTimeRestore": "2025-08-07",
      "IsAuto": "1"
    }
  ],
  "message": "success"
}
```

### POST /InsertSetupRestoreSQLDatabase
Thêm cấu hình restore database mới

**Request Body:**
```typescript
interface SetupToolRestoreSQL {
  IP: string;                 // Required - IP address
  DatabaseName: string;       // Required - Tên database
  ServerSQL: string;          // Required - SQL Server name
  UserSQL: string;            // Required - SQL username
  PasswordSQL: string;        // Required - SQL password
  Destination_folder: string; // Required - Thư mục đích
  Source_File: string;        // Required - File nguồn
  Folder_Logical: string;     // Required - Thư mục logical
  IsAuto: string;            // Required - Tự động ("1" hoặc "0")
}
```

**Example Request:**
```json
{
  "IP": "*************",
  "DatabaseName": "TestDB",
  "ServerSQL": "SQLSERVER01",
  "UserSQL": "sa",
  "PasswordSQL": "password123",
  "Destination_folder": "C:\\Backup\\",
  "Source_File": "TestDB.bak",
  "Folder_Logical": "C:\\Data\\",
  "IsAuto": "1"
}
```

**Success Response:**
```json
{
  "code": 200,
  "data": "123e4567-e89b-12d3-a456-************",  // ID của record mới tạo
  "message": "success"
}
```

**Error Responses:**
- `400`: "Invalid form data"
- `500`: Database connection hoặc query error

## TypeScript Interfaces

Để sử dụng trong frontend TypeScript/Vue.js:

```typescript
// Common Response
interface ApiResponse<T = any> {
  code: number;
  data: T | null;
  message: string;
}

// User & Authentication
interface User {
  userID: string;
  userName: string;
  password: string;
  role: string;
  token?: string;
}

interface LoginRequest {
  userID: string;
  password: string;
}

// Tool Restore SQL Database
interface ToolRestoreSQLDatabase {
  ID?: string;              // Optional for POST requests
  IP: string;
  DatabaseName: string;
  SizeFileZip: string;
  SizeBAK: string;
  DateTimeRestore?: string; // Auto-generated
  IsSuccess: string;
  LogContent: string;
}

// Setup Tool Restore SQL
interface SetupToolRestoreSQL {
  ID?: string;              // Optional for POST requests
  IP: string;
  DatabaseName: string;
  ServerSQL: string;
  UserSQL: string;
  PasswordSQL: string;
  Destination_folder: string;
  Source_File: string;
  Folder_Logical: string;
  DateTimeRestore?: string; // Auto-generated
  IsAuto: string;
}
```

## Error Handling

### Common HTTP Status Codes:
- `200`: Success
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Authentication failed
- `404`: Not Found - Route not found
- `405`: Method Not Allowed
- `500`: Internal Server Error - Database or server error

### Error Response Examples:

**Validation Error (400):**
```json
{
  "code": 400,
  "data": null,
  "message": "Invalid form data"
}
```

**Authentication Error (401):**
```json
{
  "code": 401,
  "data": null,
  "message": "Account does not exist or has been locked"
}
```

**Server Error (500):**
```json
{
  "code": 500,
  "data": null,
  "message": "failed to connect database: connection timeout"
}
```

## Usage Examples với Axios

### Setup Axios Client
```typescript
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://localhost:8003/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### Login Example
```typescript
async function login(credentials: LoginRequest): Promise<User> {
  try {
    const response = await apiClient.post<ApiResponse<User>>('/login', credentials);
    if (response.data.code === 200 && response.data.data) {
      localStorage.setItem('token', response.data.data.token || '');
      return response.data.data;
    }
    throw new Error(response.data.message);
  } catch (error) {
    throw error;
  }
}
```

### Get Restore Database List
```typescript
async function getRestoreDatabaseList(): Promise<ToolRestoreSQLDatabase[]> {
  try {
    const response = await apiClient.get<ApiResponse<ToolRestoreSQLDatabase[]>>('/GetRestoreSQLDatabase');
    if (response.data.code === 200 && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message);
  } catch (error) {
    throw error;
  }
}
```

### Insert Restore Database Record
```typescript
async function insertRestoreDatabase(data: ToolRestoreSQLDatabase): Promise<string> {
  try {
    const response = await apiClient.post<ApiResponse<string>>('/InsertRestoreSQLDatabase', data);
    if (response.data.code === 200 && response.data.data) {
      return response.data.data; // Returns the new record ID
    }
    throw new Error(response.data.message);
  } catch (error) {
    throw error;
  }
}
```

## Vue.js Composable Example

Tạo một composable để quản lý API calls:

```typescript
// composables/useApi.ts
import { ref, reactive } from 'vue'
import axios from 'axios'

const apiClient = axios.create({
  baseURL: 'http://localhost:8003/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
})

export function useApi() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  const handleRequest = async <T>(request: Promise<any>): Promise<T> => {
    loading.value = true
    error.value = null

    try {
      const response = await request
      if (response.data.code === 200) {
        return response.data.data
      }
      throw new Error(response.data.message)
    } catch (err: any) {
      error.value = err.message || 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Authentication
  const login = async (credentials: LoginRequest): Promise<User> => {
    return handleRequest(apiClient.post('/login', credentials))
  }

  // Tool Restore Database
  const getRestoreDatabaseList = async (): Promise<ToolRestoreSQLDatabase[]> => {
    return handleRequest(apiClient.get('/GetRestoreSQLDatabase'))
  }

  const insertRestoreDatabase = async (data: ToolRestoreSQLDatabase): Promise<string> => {
    return handleRequest(apiClient.post('/InsertRestoreSQLDatabase', data))
  }

  // Setup Tool Restore
  const getSetupToolRestoreList = async (): Promise<SetupToolRestoreSQL[]> => {
    return handleRequest(apiClient.get('/GetSetupToolRestoreSQL'))
  }

  const insertSetupRestoreDatabase = async (data: SetupToolRestoreSQL): Promise<string> => {
    return handleRequest(apiClient.post('/InsertSetupRestoreSQLDatabase', data))
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    login,
    getRestoreDatabaseList,
    insertRestoreDatabase,
    getSetupToolRestoreList,
    insertSetupRestoreDatabase,
  }
}
```

## Notes

1. **Date Format**: Tất cả dates được trả về dưới dạng string với format `YYYY-MM-DD`
2. **Boolean Values**: Sử dụng string `"1"` cho true và `"0"` cho false
3. **IDs**: Tất cả IDs đều là UUID strings
4. **Content-Type**: API hỗ trợ `application/json` và form data
5. **CORS**: API đã được cấu hình CORS middleware
6. **Logging**: Tất cả requests được log vào file `log/application.log`
7. **Auto-generated Fields**: `ID` và `DateTimeRestore` được tự động tạo bởi database

## Server Configuration

- **Port**: 8003 (có thể thay đổi trong `data/config.yml`)
- **Environment**: Development/Production
- **Database**: SQL Server
- **Framework**: Gin (Go)
- **ORM**: GORM
- **Base Path**: `/api/v1`

## Testing với Postman/Thunder Client

### 1. Health Check
```
GET http://localhost:8003/api/v1/ping
```

### 2. Login
```
POST http://localhost:8003/api/v1/login
Content-Type: application/json

{
  "userID": "51295",
  "password": "your_password"
}
```

### 3. Get Restore Database List
```
GET http://localhost:8003/api/v1/GetRestoreSQLDatabase
```

### 4. Insert Restore Database
```
POST http://localhost:8003/api/v1/InsertRestoreSQLDatabase
Content-Type: application/json

{
  "IP": "*************",
  "DatabaseName": "TestDB",
  "SizeFileZip": "100MB",
  "SizeBAK": "250MB",
  "IsSuccess": "1",
  "LogContent": "Restore completed successfully"
}
```

---

**Lưu ý**: Tài liệu này được tạo dựa trên phân tích mã nguồn hiện tại. Nếu có thay đổi trong API, vui lòng cập nhật tài liệu tương ứng.
