<!-- 
  Dashboard Overview Component Example
  File: src/views/dashboard/OverviewDashboard.vue
-->
<template>
  <div class="dashboard-overview">
    <!-- <PERSON> Header -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">Tool Restore Database Dashboard</h1>
      <div class="dashboard-actions">
        <button @click="refreshData" :disabled="loading" class="btn-refresh">
          <RefreshIcon :class="{ 'animate-spin': loading }" />
          Refresh
        </button>
        <select v-model="selectedPeriod" @change="onPeriodChange" class="period-select">
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
        </select>
      </div>
    </div>

    <!-- KPI Cards Row -->
    <div class="kpi-grid">
      <KPICard
        title="Total Restores"
        :value="overviewData?.totalRestores || 0"
        :trend="restoreTrend"
        icon="DatabaseIcon"
        color="blue"
      />
      <KPICard
        title="Success Rate"
        :value="overviewData?.successRate || 0"
        suffix="%"
        :trend="successRateTrend"
        icon="CheckCircleIcon"
        color="green"
      />
      <KPICard
        title="Active Servers"
        :value="overviewData?.activeServers || 0"
        :trend="serverTrend"
        icon="ServerIcon"
        color="purple"
      />
      <KPICard
        title="Configurations"
        :value="overviewData?.totalConfigs || 0"
        :trend="configTrend"
        icon="CogIcon"
        color="orange"
      />
    </div>

    <!-- Charts Row -->
    <div class="charts-grid">
      <!-- Daily Trend Chart -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>Daily Restore Trend</h3>
          <div class="chart-legend">
            <span class="legend-item success">Success</span>
            <span class="legend-item failed">Failed</span>
          </div>
        </div>
        <LineChart
          :data="trendChartData"
          :options="trendChartOptions"
          height="300"
        />
      </div>

      <!-- Success Rate Pie Chart -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>Success vs Failed</h3>
        </div>
        <PieChart
          :data="successRateChartData"
          :options="pieChartOptions"
          height="300"
        />
      </div>
    </div>

    <!-- Tables Row -->
    <div class="tables-grid">
      <!-- Recent Activity Table -->
      <div class="table-container">
        <div class="table-header">
          <h3>Recent Activity</h3>
          <router-link to="/dashboard/restores" class="view-all-link">
            View All
          </router-link>
        </div>
        <div class="table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th>Time</th>
                <th>Database</th>
                <th>Server</th>
                <th>Status</th>
                <th>Size</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in recentActivity" :key="item.id">
                <td>{{ formatDateTime(item.dateTimeRestore) }}</td>
                <td>{{ item.databaseName }}</td>
                <td>{{ item.ip }}</td>
                <td>
                  <StatusBadge :status="item.isSuccess" />
                </td>
                <td>{{ item.sizeBAK }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Top Databases Table -->
      <div class="table-container">
        <div class="table-header">
          <h3>Top Databases</h3>
        </div>
        <div class="table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th>Database</th>
                <th>Count</th>
                <th>Success Rate</th>
                <th>Last Restore</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="db in topDatabases" :key="db.databaseName">
                <td>{{ db.databaseName }}</td>
                <td>{{ db.restoreCount }}</td>
                <td>
                  <ProgressBar :value="db.successRate" />
                </td>
                <td>{{ formatDateTime(db.lastRestoreTime) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Alerts Section -->
    <div v-if="alerts.length > 0" class="alerts-section">
      <h3>Active Alerts</h3>
      <div class="alerts-grid">
        <AlertCard
          v-for="alert in alerts"
          :key="alert.alertType"
          :alert="alert"
          @dismiss="dismissAlert"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import { formatDateTime } from '@/utils/dateUtils'
import KPICard from '@/components/dashboard/KPICard.vue'
import LineChart from '@/components/charts/LineChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import StatusBadge from '@/components/ui/StatusBadge.vue'
import ProgressBar from '@/components/ui/ProgressBar.vue'
import AlertCard from '@/components/dashboard/AlertCard.vue'
import { RefreshIcon, DatabaseIcon, CheckCircleIcon, ServerIcon, CogIcon } from '@heroicons/vue/24/outline'

// Store
const dashboardStore = useDashboardStore()

// Reactive data
const selectedPeriod = ref(30)
const loading = ref(false)
const refreshInterval = ref<NodeJS.Timeout | null>(null)

// Computed properties
const overviewData = computed(() => dashboardStore.overviewData)
const recentActivity = computed(() => dashboardStore.recentActivity)
const topDatabases = computed(() => dashboardStore.topDatabases)
const alerts = computed(() => dashboardStore.alerts)

// Trend calculations (mock data - replace with actual calculations)
const restoreTrend = computed(() => ({ direction: 'up', percentage: 12.5 }))
const successRateTrend = computed(() => ({ direction: 'up', percentage: 3.2 }))
const serverTrend = computed(() => ({ direction: 'stable', percentage: 0 }))
const configTrend = computed(() => ({ direction: 'down', percentage: -2.1 }))

// Chart data
const trendChartData = computed(() => ({
  labels: dashboardStore.trendData?.map(d => d.date) || [],
  datasets: [
    {
      label: 'Success',
      data: dashboardStore.trendData?.map(d => d.successCount) || [],
      borderColor: '#10B981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4
    },
    {
      label: 'Failed',
      data: dashboardStore.trendData?.map(d => d.failureCount) || [],
      borderColor: '#EF4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      tension: 0.4
    }
  ]
}))

const successRateChartData = computed(() => ({
  labels: ['Success', 'Failed'],
  datasets: [{
    data: [
      overviewData.value?.successfulRestores || 0,
      (overviewData.value?.totalRestores || 0) - (overviewData.value?.successfulRestores || 0)
    ],
    backgroundColor: ['#10B981', '#EF4444'],
    borderWidth: 0
  }]
}))

// Chart options
const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: '#E5E7EB'
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
}

const pieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const
    }
  }
}

// Methods
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      dashboardStore.fetchOverviewData(selectedPeriod.value),
      dashboardStore.fetchRecentActivity(),
      dashboardStore.fetchTopDatabases(selectedPeriod.value),
      dashboardStore.fetchAlerts()
    ])
  } catch (error) {
    console.error('Failed to refresh dashboard data:', error)
  } finally {
    loading.value = false
  }
}

const onPeriodChange = () => {
  refreshData()
}

const dismissAlert = (alertType: string) => {
  dashboardStore.dismissAlert(alertType)
}

const startAutoRefresh = () => {
  refreshInterval.value = setInterval(refreshData, 5 * 60 * 1000) // 5 minutes
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// Lifecycle
onMounted(() => {
  refreshData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.dashboard-overview {
  @apply p-6 space-y-6;
}

.dashboard-header {
  @apply flex justify-between items-center;
}

.dashboard-title {
  @apply text-2xl font-bold text-gray-900;
}

.dashboard-actions {
  @apply flex items-center space-x-4;
}

.btn-refresh {
  @apply flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50;
}

.period-select {
  @apply px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500;
}

.kpi-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.charts-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.tables-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.chart-container {
  @apply bg-white rounded-lg shadow p-6;
}

.chart-header {
  @apply flex justify-between items-center mb-4;
}

.chart-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.chart-legend {
  @apply flex space-x-4;
}

.legend-item {
  @apply flex items-center space-x-2 text-sm;
}

.legend-item::before {
  @apply w-3 h-3 rounded-full;
  content: '';
}

.legend-item.success::before {
  @apply bg-green-500;
}

.legend-item.failed::before {
  @apply bg-red-500;
}

.table-container {
  @apply bg-white rounded-lg shadow;
}

.table-header {
  @apply flex justify-between items-center p-6 border-b border-gray-200;
}

.table-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.view-all-link {
  @apply text-blue-600 hover:text-blue-800 text-sm font-medium;
}

.table-wrapper {
  @apply overflow-x-auto;
}

.data-table {
  @apply w-full;
}

.data-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.data-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.alerts-section {
  @apply space-y-4;
}

.alerts-section h3 {
  @apply text-lg font-semibold text-gray-900;
}

.alerts-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
