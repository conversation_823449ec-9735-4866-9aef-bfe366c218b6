package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

// RegisterDashboardRouter - <PERSON><PERSON>ng ký các routes cho Dashboard API
func RegisterDashboardRouter(router *gin.RouterGroup) {
	dashboardGroup := router.Group("/dashboard")
	{
		// =============================================
		// OVERVIEW DASHBOARD ROUTES
		// =============================================
		dashboardGroup.GET("/overview", controllers.Dashboard.GetOverview)
		dashboardGroup.GET("/summary", controllers.Dashboard.GetDashboardSummary)

		// =============================================
		// RESTORE OPERATIONS ROUTES
		// =============================================
		dashboardGroup.GET("/restore-trend", controllers.Dashboard.GetRestoreTrend)
		dashboardGroup.GET("/top-databases", controllers.Dashboard.GetTopDatabases)
		dashboardGroup.GET("/server-stats", controllers.Dashboard.GetServerStats)

		// =============================================
		// SETUP CONFIGURATION ROUTES
		// =============================================
		dashboardGroup.GET("/setup-stats", controllers.Dashboard.GetSetupStats)
		dashboardGroup.GET("/config-usage", controllers.Dashboard.GetConfigUsage)
		dashboardGroup.GET("/config-effectiveness", controllers.Dashboard.GetConfigEffectiveness)

		// =============================================
		// PERFORMANCE DASHBOARD ROUTES
		// =============================================
		dashboardGroup.GET("/hourly-performance", controllers.Dashboard.GetHourlyPerformance)
		dashboardGroup.GET("/size-distribution", controllers.Dashboard.GetSizeDistribution)

		// =============================================
		// ALERTS & MONITORING ROUTES
		// =============================================
		dashboardGroup.GET("/alerts", controllers.Dashboard.GetAlerts)
		dashboardGroup.GET("/health-check", controllers.Dashboard.GetHealthCheck)

		// =============================================
		// DETAILED ANALYSIS ROUTES
		// =============================================
		dashboardGroup.GET("/restore-details", controllers.Dashboard.GetRestoreDetails)

		// =============================================
		// UTILITY ROUTES
		// =============================================
		dashboardGroup.POST("/refresh", controllers.Dashboard.RefreshDashboard)
	}
}

/*
API ENDPOINTS DOCUMENTATION:

BASE URL: /api/v1/dashboard

1. OVERVIEW DASHBOARD:
   GET /overview
   - Trả về thống kê tổng quan và hoạt động gần đây
   - Response: { stats: OverviewStats, activity: RecentActivity[] }

   GET /summary?days=30
   - Trả về tổng hợp tất cả thông tin dashboard chính
   - Parameters: days (optional, default: 30)
   - Response: { overview, recentActivity, topDatabases, alerts, systemHealth }

2. RESTORE OPERATIONS:
   GET /restore-trend?days=30
   - Xu hướng restore theo ngày
   - Parameters: days (optional, default: 30)
   - Response: DailyTrend[]

   GET /top-databases?days=30&limit=10
   - Top databases được restore nhiều nhất
   - Parameters: days (optional, default: 30), limit (optional, default: 10)
   - Response: TopDatabase[]

   GET /server-stats?days=30
   - Thống kê theo server/IP
   - Parameters: days (optional, default: 30)
   - Response: ServerStats[]

3. SETUP CONFIGURATION:
   GET /setup-stats
   - Thống kê cấu hình setup
   - Response: SetupConfigStats[]

   GET /config-usage
   - So sánh setup vs actual restore
   - Response: ConfigUsageComparison[]

   GET /config-effectiveness
   - Phân tích hiệu quả cấu hình
   - Response: ConfigEffectiveness[]

4. PERFORMANCE DASHBOARD:
   GET /hourly-performance?days=7
   - Hiệu suất theo giờ trong ngày
   - Parameters: days (optional, default: 7)
   - Response: HourlyPerformance[]

   GET /size-distribution?days=30
   - Phân bố kích thước file
   - Parameters: days (optional, default: 30)
   - Response: SizeDistribution[]

5. ALERTS & MONITORING:
   GET /alerts
   - Danh sách cảnh báo hệ thống
   - Response: Alert[]

   GET /health-check
   - Kiểm tra sức khỏe hệ thống
   - Response: SystemHealth

6. DETAILED ANALYSIS:
   GET /restore-details?page=1&size=50&days=30&database=&ip=&status=&sort=DateTimeRestore&order=desc
   - Chi tiết restore với pagination và filtering
   - Parameters:
     * page (optional, default: 1)
     * size (optional, default: 50)
     * days (optional, default: 30)
     * database (optional, filter by database name)
     * ip (optional, filter by IP)
     * status (optional, "success" or "failed")
     * sort (optional, default: "DateTimeRestore")
     * order (optional, "asc" or "desc", default: "desc")
   - Response: PaginatedRestoreDetails

7. UTILITY:
   POST /refresh
   - Trigger manual refresh của dashboard data
   - Response: { message: "success" }

ERROR RESPONSES:
- 400: Bad Request - Invalid parameters
- 500: Internal Server Error - Database or server error

All responses follow the standard format:
{
  "code": 200,
  "data": {...},
  "message": "success"
}
*/
