package controllers

import (
	"net/http"
	"strconv"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type DashboardController struct {
	*BaseController
}

var Dashboard = &DashboardController{}

// =============================================
// OVERVIEW DASHBOARD ENDPOINTS
// =============================================

// GetOverview - GET /api/dashboard/overview
func (c *DashboardController) GetOverview(ctx *gin.Context) {
	overviewStats, err := services.Dashboard.GetOverviewStats(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	recentActivity, err := services.Dashboard.GetRecentActivity(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	result := gin.H{
		"stats":    overviewStats,
		"activity": recentActivity,
	}

	response.OkWithData(ctx, result)
}

// =============================================
// RESTORE OPERATIONS ENDPOINTS
// =============================================

// GetRestoreTrend - GET /api/dashboard/restore-trend?days=30
func (c *DashboardController) GetRestoreTrend(ctx *gin.Context) {
	var params types.DashboardQueryParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid query parameters")
		return
	}

	// Set default days if not provided
	if params.Days == 0 {
		params.Days = 30
	}

	trends, err := services.Dashboard.GetRestoreTrend(ctx, params.Days)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, trends)
}

// GetTopDatabases - GET /api/dashboard/top-databases?days=30&limit=10
func (c *DashboardController) GetTopDatabases(ctx *gin.Context) {
	var params types.DashboardQueryParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid query parameters")
		return
	}

	// Set defaults
	if params.Days == 0 {
		params.Days = 30
	}
	if params.Limit == 0 {
		params.Limit = 10
	}

	databases, err := services.Dashboard.GetTopDatabases(ctx, params.Days, params.Limit)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, databases)
}

// GetServerStats - GET /api/dashboard/server-stats?days=30
func (c *DashboardController) GetServerStats(ctx *gin.Context) {
	var params types.DashboardQueryParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid query parameters")
		return
	}

	if params.Days == 0 {
		params.Days = 30
	}

	servers, err := services.Dashboard.GetServerStats(ctx, params.Days)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, servers)
}

// =============================================
// SETUP CONFIGURATION ENDPOINTS
// =============================================

// GetSetupStats - GET /api/dashboard/setup-stats
func (c *DashboardController) GetSetupStats(ctx *gin.Context) {
	configs, err := services.Dashboard.GetSetupStats(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, configs)
}

// GetConfigUsage - GET /api/dashboard/config-usage
func (c *DashboardController) GetConfigUsage(ctx *gin.Context) {
	comparisons, err := services.Dashboard.GetConfigUsageComparison(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, comparisons)
}

// GetConfigEffectiveness - GET /api/dashboard/config-effectiveness
func (c *DashboardController) GetConfigEffectiveness(ctx *gin.Context) {
	effectiveness, err := services.Dashboard.GetConfigEffectiveness(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, effectiveness)
}

// =============================================
// PERFORMANCE DASHBOARD ENDPOINTS
// =============================================

// GetHourlyPerformance - GET /api/dashboard/hourly-performance?days=7
func (c *DashboardController) GetHourlyPerformance(ctx *gin.Context) {
	var params types.DashboardQueryParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid query parameters")
		return
	}

	if params.Days == 0 {
		params.Days = 7
	}

	performance, err := services.Dashboard.GetHourlyPerformance(ctx, params.Days)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, performance)
}

// GetSizeDistribution - GET /api/dashboard/size-distribution?days=30
func (c *DashboardController) GetSizeDistribution(ctx *gin.Context) {
	var params types.DashboardQueryParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid query parameters")
		return
	}

	if params.Days == 0 {
		params.Days = 30
	}

	distribution, err := services.Dashboard.GetSizeDistribution(ctx, params.Days)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, distribution)
}

// =============================================
// ALERTS & MONITORING ENDPOINTS
// =============================================

// GetAlerts - GET /api/dashboard/alerts
func (c *DashboardController) GetAlerts(ctx *gin.Context) {
	alerts, err := services.Dashboard.GetAlerts(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, alerts)
}

// GetHealthCheck - GET /api/dashboard/health-check
func (c *DashboardController) GetHealthCheck(ctx *gin.Context) {
	health, err := services.Dashboard.GetSystemHealth(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, health)
}

// =============================================
// DETAILED ANALYSIS ENDPOINTS
// =============================================

// GetRestoreDetails - GET /api/dashboard/restore-details?page=1&size=50&days=30&database=&ip=&status=&sort=DateTimeRestore&order=desc
func (c *DashboardController) GetRestoreDetails(ctx *gin.Context) {
	var params types.RestoreDetailsParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid query parameters")
		return
	}

	details, err := services.Dashboard.GetRestoreDetails(ctx, params)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, details)
}

// =============================================
// UTILITY ENDPOINTS
// =============================================

// GetDashboardSummary - GET /api/dashboard/summary (Combined endpoint for quick overview)
func (c *DashboardController) GetDashboardSummary(ctx *gin.Context) {
	// Get days parameter
	daysStr := ctx.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	// Fetch all summary data concurrently
	type summaryResult struct {
		Overview     *types.OverviewStats      `json:"overview"`
		Activity     []types.RecentActivity    `json:"recentActivity"`
		TopDatabases []types.TopDatabase       `json:"topDatabases"`
		Alerts       []types.Alert             `json:"alerts"`
		Health       *types.SystemHealth       `json:"systemHealth"`
	}

	var result summaryResult

	// Get overview stats
	if overview, err := services.Dashboard.GetOverviewStats(ctx); err == nil {
		result.Overview = overview
	}

	// Get recent activity
	if activity, err := services.Dashboard.GetRecentActivity(ctx); err == nil {
		result.Activity = activity
	}

	// Get top 5 databases
	if databases, err := services.Dashboard.GetTopDatabases(ctx, days, 5); err == nil {
		result.TopDatabases = databases
	}

	// Get alerts
	if alerts, err := services.Dashboard.GetAlerts(ctx); err == nil {
		result.Alerts = alerts
	}

	// Get system health
	if health, err := services.Dashboard.GetSystemHealth(ctx); err == nil {
		result.Health = health
	}

	response.OkWithData(ctx, result)
}

// RefreshDashboard - POST /api/dashboard/refresh (Trigger manual refresh)
func (c *DashboardController) RefreshDashboard(ctx *gin.Context) {
	// This endpoint can be used to trigger cache refresh or other maintenance tasks
	// For now, just return success
	response.OkWithMessage(ctx, "Dashboard data refresh triggered successfully")
}
