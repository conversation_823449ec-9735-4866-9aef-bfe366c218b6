
2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[2.237ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[4.351ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:48:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:58 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[2.496ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:48:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:59 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[2.868ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[3.358ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[2.170ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[6.221ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 13:02:17 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:02:17 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:02:17 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[71.868ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:02:17 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:02:17 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:02:17 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[13.315ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	
