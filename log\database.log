
2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.341ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.511ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.374ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[6.479ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[8.998ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:17:20 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[20.286ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:11 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.996ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.594ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[5.111ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[5.111ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[5.111ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:18:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.675ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.649ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.649ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[5.152ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[6.186ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[13.335ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:22:12 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.679ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.944ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.758ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.101ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[5.722ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.833ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:22:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.504ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:23:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:01 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[4.177ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[1.775ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.368ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.505ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.551ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[3.839ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.344ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:23:02 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.515ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.240ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.526ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[3.666ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[5.115ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[5.450ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:23:04 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.344ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.324ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.809ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.994ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.071ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.071ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:26:47 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.035ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.230ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.230ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[2.805ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[5.249ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.179ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:49 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.900ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.399ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[2.494ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[2.494ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[3.000ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[3.000ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:50 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.706ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.946ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.944ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.726ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.726ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[8.002ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:26:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.412ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[6.003ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[6.721ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[6.721ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[5.965ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[10.296ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:26:58 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.140ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[2.214ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[6.117ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.304ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.600ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[5.529ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.500ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:26:59 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[5.158ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.951ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[7.939ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[6.295ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[6.848ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.354ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.507ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.326ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.348ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.329ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[5.394ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.943ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:14 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.192ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.566ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.285ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[2.600ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[3.074ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.186ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.116ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.423ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[3.088ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[3.887ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.408ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[8.029ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:15 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.548ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.841ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.841ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[6.095ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:26 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.919ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:27 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[6.879ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:27 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:27 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:27 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.653ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.014ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.014ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[7.303ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[7.303ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[7.303ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:38 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[3.004ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[3.073ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.591ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.660ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[4.135ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.749ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:39 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[0.525ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.265ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.796ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.319ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[3.820ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.848ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:52 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.227ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.517ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.024ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.820ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[3.354ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[3.882ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:27:56 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.514ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.985ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.985ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[1.985ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[3.273ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[3.779ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:28:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.176ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.602ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.531ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.089ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[5.391ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[7.330ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:28:07 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.482ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.334ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.803ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[5.858ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[6.787ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[7.413ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:19 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.301ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.919ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.950ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.583ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[7.692ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[19.118ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:28:41 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.892ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[17.867ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[24.700ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[22.543ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[22.543ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[22.543ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:28:51 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.583ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.827ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.634ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.220ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.534ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[7.067ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:28:53 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[2.200ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.827ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.933ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[8.207ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[10.197ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[7.813ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:01 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.981ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[1.806ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[1.071ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -30, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[3.615ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[4.973ms] [rows:30] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -29, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 29
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[5.531ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:29:34 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.126ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:143
[3.898ms] [rows:7] 
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -6, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= 6
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:58
[4.409ms] [rows:1] 
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:182
[2.265ms] [rows:1] 
		SELECT TOP (10)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:582
[2.204ms] [rows:1] 
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:541 mssql: Ambiguous column name 'IP'.
[4.846ms] [rows:-] 
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	

2025/08/08 13:29:54 E:/api_tool_restore_database_sql/internal/api/services/dashboard_service.go:93
[1.859ms] [rows:2] 
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	
