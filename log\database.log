
2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[2.237ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:57 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[4.351ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:48:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:58 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:58 E:/api_tool_restore_database_sql/internal/api/services/SetupToolRestoreSQL_service.go:39
[2.496ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			ServerSQL,
			UserSQL,
			PasswordSQL,
			Destination_folder,
			Source_File,
			Folder_Logical,
			isAuto
		FROM SetupToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:48:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:59 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:48:59 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[2.868ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[3.358ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[2.170ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:create` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75
[info] replacing callback `gorm:update` from E:/api_tool_restore_database_sql/internal/pkg/database/database.go:75

2025/08/08 12:49:03 E:/api_tool_restore_database_sql/internal/api/services/ToolRestoreSQL_service.go:37
[6.221ms] [rows:1] 
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	
