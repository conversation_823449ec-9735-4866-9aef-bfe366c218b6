package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type CommonController struct {
	*BaseController
}

var Common = &CommonController{}

func (c *CommonController) Ping(ctx *gin.Context) {
	response.Ok(ctx)
}

func (c *CommonController) PingSQL(ctx *gin.Context) {
	db, err := database.DatabaseConnection()
	if err != nil {
		response.FailWithMessage(ctx, "Failed to connect to database: "+err.Error())
		return
	}

	sqlDB, err := db.DB()
	if err != nil {
		response.FailWithMessage(ctx, "Failed to get underlying sql.DB: "+err.Error())
		return
	}

	err = sqlDB.Ping()
	if err != nil {
		response.FailWithMessage(ctx, "Database ping failed: "+err.<PERSON>rror())
		return
	}

	response.OkWithMessage(ctx, "Database connection is healthy")
}

func (c *CommonController) Login(ctx *gin.Context) {
	var requestParams request.LoginRequest // dùng struct riêng cho login
	if err := ctx.ShouldBindJSON(&requestParams); err != nil {
		log.Printf("Invalid JSON: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid input format")
		return
	}

	result, err := services.Common.Login(&types.User{
		UserID:   requestParams.UserID,
		Password: requestParams.Password,
	})
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusUnauthorized, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}
