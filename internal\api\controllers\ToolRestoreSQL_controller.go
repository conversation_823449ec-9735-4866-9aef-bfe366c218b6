package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type ToolRestoreServicesController struct {
	*BaseController
}

var ToolRestoreSQLDatabase = &ToolRestoreServicesController{}



func (c *ToolRestoreServicesController) GetRestoreSQLDatabase(ctx *gin.Context) {
	result, err := services.ToolRestore.GetRestoreSQLDatabase(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}



func (c *ToolRestoreServicesController) InsertRestoreSQLDatabase(ctx *gin.Context) {
	var requestParams *types.ToolRestoreSQLDatabase
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.ToolRestore.InsertRestoreSQLDatabase(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}