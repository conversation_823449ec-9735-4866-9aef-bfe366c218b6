package services

import (
	"context"
	"fmt"
	"math"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type DashboardService struct {
	*BaseService
}

var Dashboard = &DashboardService{}

// =============================================
// OVERVIEW DASHBOARD SERVICES
// =============================================

// GetOverviewStats - L<PERSON>y thống kê tổng quan hệ thống
func (s *DashboardService) GetOverviewStats(ctx context.Context) (*types.OverviewStats, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var stats types.OverviewStats
	query := `
		SELECT 
			-- Thống kê Restore Operations
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) as TotalRestoreOperations,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') as SuccessfulRestores,
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '0') as FailedRestores,
			
			-- Tỷ lệ thành công
			CASE 
				WHEN (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) > 0 
				THEN CAST((SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase] WHERE isSuccess = '1') * 100.0 / 
						  (SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]) AS DECIMAL(5,2))
				ELSE 0 
			END as SuccessRate,
			
			-- Thống kê Setup Configurations
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]) as TotalSetupConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '1') as AutoConfigs,
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase] WHERE isAuto = '0') as ManualConfigs,
			
			-- Thống kê Server/IP
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueServersWithRestores,
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueServersWithSetup,
			
			-- Thống kê Database
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[ToolRestoreSQLDatabase]) as UniqueDatabasesRestored,
			(SELECT COUNT(DISTINCT DatabaseName) FROM [dbo].[SetupToolRestoreSQLDatabase]) as UniqueDatabasesConfigured
	`

	if err := db.Raw(query).Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("failed to execute overview stats query: %v", err)
	}

	return &stats, nil
}

// GetRecentActivity - Lấy hoạt động gần đây (24h)
func (s *DashboardService) GetRecentActivity(ctx context.Context) ([]types.RecentActivity, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var activities []types.RecentActivity
	query := `
		SELECT 
			'RESTORE_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[ToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())

		UNION ALL

		SELECT 
			'SETUP_ACTIVITY' as ActivityType,
			COUNT(*) as Count,
			SUM(CASE WHEN isAuto = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN isAuto = '0' THEN 1 ELSE 0 END) as FailureCount
		FROM [dbo].[SetupToolRestoreSQLDatabase] 
		WHERE DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
	`

	if err := db.Raw(query).Scan(&activities).Error; err != nil {
		return nil, fmt.Errorf("failed to execute recent activity query: %v", err)
	}

	return activities, nil
}

// =============================================
// RESTORE OPERATIONS SERVICES
// =============================================

// GetRestoreTrend - Lấy xu hướng restore theo ngày
func (s *DashboardService) GetRestoreTrend(ctx context.Context, days int) ([]types.DailyTrend, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var trends []types.DailyTrend
	query := `
		WITH DateRange AS (
			SELECT CAST(DATEADD(DAY, number, DATEADD(DAY, -?, GETDATE())) AS DATE) as RestoreDate
			FROM master..spt_values 
			WHERE type = 'p' AND number <= ?
		)
		SELECT 
			FORMAT(dr.RestoreDate, 'dd/MM/yyyy') as Date,
			CAST(dr.RestoreDate AS VARCHAR) as RestoreDate,
			ISNULL(COUNT(t.ID), 0) as TotalRestores,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,
			
			-- Tính tỷ lệ thành công
			CASE 
				WHEN COUNT(t.ID) > 0 
				THEN CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(t.ID) AS DECIMAL(5,2))
				ELSE 0 
			END as DailySuccessRate,
			
			-- Thống kê size (convert từ string sang numeric)
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)), 0) as AvgSizeZip
			
		FROM DateRange dr
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t 
			ON CAST(t.DateTimeRestore AS DATE) = dr.RestoreDate
		GROUP BY dr.RestoreDate
		ORDER BY dr.RestoreDate
	`

	if err := db.Raw(query, days-1, days-1).Scan(&trends).Error; err != nil {
		return nil, fmt.Errorf("failed to execute restore trend query: %v", err)
	}

	return trends, nil
}

// GetTopDatabases - Lấy top databases được restore nhiều nhất
func (s *DashboardService) GetTopDatabases(ctx context.Context, days, limit int) ([]types.TopDatabase, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var databases []types.TopDatabase
	query := `
		SELECT TOP (?)
			t.DatabaseName,
			COUNT(*) as RestoreCount,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Size statistics
			AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeBAK,
			MAX(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeBAK,
			
			-- Thời gian restore gần nhất
			MAX(t.DateTimeRestore) as LastRestoreTime,
			DATEDIFF(HOUR, MAX(t.DateTimeRestore), GETDATE()) as HoursSinceLastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -?, GETDATE())
		GROUP BY t.DatabaseName
		ORDER BY RestoreCount DESC
	`

	if err := db.Raw(query, limit, days).Scan(&databases).Error; err != nil {
		return nil, fmt.Errorf("failed to execute top databases query: %v", err)
	}

	return databases, nil
}

// GetServerStats - Lấy thống kê theo Server/IP
func (s *DashboardService) GetServerStats(ctx context.Context, days int) ([]types.ServerStats, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var servers []types.ServerStats
	query := `
		SELECT 
			t.IP,
			COUNT(*) as TotalRestores,
			SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
			
			-- Tỷ lệ thành công
			CAST(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,
			
			-- Số database unique trên server
			COUNT(DISTINCT t.DatabaseName) as UniqueDbCount,
			
			-- Tổng dung lượng đã restore thành công
			SUM(CASE WHEN t.isSuccess = '1' THEN TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT) ELSE 0 END) as TotalRestoredSizeMB,
			
			-- Thời gian hoạt động
			MIN(t.DateTimeRestore) as FirstRestore,
			MAX(t.DateTimeRestore) as LastRestore
			
		FROM [dbo].[ToolRestoreSQLDatabase] t
		WHERE t.DateTimeRestore >= DATEADD(DAY, -?, GETDATE())
		GROUP BY t.IP
		ORDER BY TotalRestores DESC
	`

	if err := db.Raw(query, days).Scan(&servers).Error; err != nil {
		return nil, fmt.Errorf("failed to execute server stats query: %v", err)
	}

	return servers, nil
}

// =============================================
// SETUP CONFIGURATION SERVICES
// =============================================

// GetSetupStats - Lấy thống kê Setup Configuration
func (s *DashboardService) GetSetupStats(ctx context.Context) ([]types.SetupConfigStats, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var configs []types.SetupConfigStats
	query := `
		SELECT 
			s.IP,
			s.ServerSQL,
			COUNT(*) as ConfigCount,
			SUM(CASE WHEN s.isAuto = '1' THEN 1 ELSE 0 END) as AutoConfigCount,
			SUM(CASE WHEN s.isAuto = '0' THEN 1 ELSE 0 END) as ManualConfigCount,
			
			-- Tỷ lệ tự động
			CAST(SUM(CASE WHEN s.isAuto = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as AutoPercentage,
			
			-- Số database được cấu hình
			COUNT(DISTINCT s.DatabaseName) as UniqueDbConfigured,
			
			-- Thời gian cấu hình
			MIN(s.DateTimeRestore) as FirstConfigTime,
			MAX(s.DateTimeRestore) as LastConfigTime,
			
			-- Kiểm tra có restore thực tế không
			(SELECT COUNT(*) 
			 FROM [dbo].[ToolRestoreSQLDatabase] t 
			 WHERE t.IP = s.IP) as ActualRestoreCount
			 
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		GROUP BY s.IP, s.ServerSQL
		ORDER BY ConfigCount DESC
	`

	if err := db.Raw(query).Scan(&configs).Error; err != nil {
		return nil, fmt.Errorf("failed to execute setup stats query: %v", err)
	}

	return configs, nil
}

// GetConfigUsageComparison - So sánh Setup vs Actual Restore
func (s *DashboardService) GetConfigUsageComparison(ctx context.Context) ([]types.ConfigUsageComparison, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var comparisons []types.ConfigUsageComparison
	query := `
		SELECT
			'CONFIGURED_BUT_NOT_USED' as Status,
			s.IP,
			s.DatabaseName,
			s.ServerSQL,
			s.DateTimeRestore as ConfigTime,
			CASE WHEN s.isAuto = '1' THEN 'Auto' ELSE 'Manual' END as ConfigType
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL

		UNION ALL

		SELECT
			'USED_WITHOUT_CONFIG' as Status,
			t.IP,
			t.DatabaseName,
			'N/A' as ServerSQL,
			t.DateTimeRestore as ConfigTime,
			'Unknown' as ConfigType
		FROM [dbo].[ToolRestoreSQLDatabase] t
		LEFT JOIN [dbo].[SetupToolRestoreSQLDatabase] s ON t.IP = s.IP AND t.DatabaseName = s.DatabaseName
		WHERE s.ID IS NULL
		ORDER BY ConfigTime DESC
	`

	if err := db.Raw(query).Scan(&comparisons).Error; err != nil {
		return nil, fmt.Errorf("failed to execute config usage comparison query: %v", err)
	}

	return comparisons, nil
}

// GetConfigEffectiveness - Phân tích hiệu quả cấu hình
func (s *DashboardService) GetConfigEffectiveness(ctx context.Context) ([]types.ConfigEffectiveness, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var effectiveness []types.ConfigEffectiveness
	query := `
		WITH ConfigUsage AS (
			SELECT
				s.IP,
				s.DatabaseName,
				s.ServerSQL,
				s.isAuto,
				s.DateTimeRestore as ConfigTime,
				COUNT(t.ID) as RestoreCount,
				SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END) as SuccessfulRestores
			FROM [dbo].[SetupToolRestoreSQLDatabase] s
			LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t
				ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
				AND t.DateTimeRestore >= s.DateTimeRestore
			GROUP BY s.IP, s.DatabaseName, s.ServerSQL, s.isAuto, s.DateTimeRestore
		)
		SELECT
			IP,
			DatabaseName,
			ServerSQL,
			CASE WHEN isAuto = '1' THEN 'Auto' ELSE 'Manual' END as ConfigType,
			ConfigTime,
			RestoreCount,
			SuccessfulRestores,

			-- Effectiveness metrics
			CASE
				WHEN RestoreCount = 0 THEN 'Not Used'
				WHEN SuccessfulRestores = RestoreCount THEN 'Highly Effective'
				WHEN SuccessfulRestores > RestoreCount * 0.8 THEN 'Effective'
				WHEN SuccessfulRestores > RestoreCount * 0.5 THEN 'Moderately Effective'
				ELSE 'Low Effectiveness'
			END as EffectivenessRating,

			CASE
				WHEN RestoreCount > 0
				THEN CAST(SuccessfulRestores * 100.0 / RestoreCount AS DECIMAL(5,2))
				ELSE 0
			END as SuccessRate

		FROM ConfigUsage
		ORDER BY RestoreCount DESC, SuccessfulRestores DESC
	`

	if err := db.Raw(query).Scan(&effectiveness).Error; err != nil {
		return nil, fmt.Errorf("failed to execute config effectiveness query: %v", err)
	}

	return effectiveness, nil
}

// =============================================
// PERFORMANCE & MONITORING SERVICES
// =============================================

// GetHourlyPerformance - Lấy hiệu suất theo giờ trong ngày
func (s *DashboardService) GetHourlyPerformance(ctx context.Context, days int) ([]types.HourlyPerformance, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var performance []types.HourlyPerformance
	query := `
		WITH Hours AS (
			SELECT number as HourOfDay
			FROM master..spt_values
			WHERE type = 'p' AND number BETWEEN 0 AND 23
		)
		SELECT
			h.HourOfDay,
			FORMAT(h.HourOfDay, '00') + ':00' as HourLabel,

			-- Restore statistics
			ISNULL(COUNT(t.ID), 0) as RestoreCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '1' THEN 1 ELSE 0 END), 0) as SuccessCount,
			ISNULL(SUM(CASE WHEN t.isSuccess = '0' THEN 1 ELSE 0 END), 0) as FailureCount,

			-- Average size during this hour
			ISNULL(AVG(TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)), 0) as AvgSizeBAK,

			-- Performance classification
			CASE
				WHEN h.HourOfDay BETWEEN 8 AND 17 THEN 'Business Hours'
				WHEN h.HourOfDay BETWEEN 18 AND 22 THEN 'Evening'
				WHEN h.HourOfDay BETWEEN 23 AND 6 THEN 'Night'
				ELSE 'Early Morning'
			END as TimePeriod

		FROM Hours h
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t
			ON DATEPART(HOUR, t.DateTimeRestore) = h.HourOfDay
			AND t.DateTimeRestore >= DATEADD(DAY, -?, GETDATE())
		GROUP BY h.HourOfDay
		ORDER BY h.HourOfDay
	`

	if err := db.Raw(query, days).Scan(&performance).Error; err != nil {
		return nil, fmt.Errorf("failed to execute hourly performance query: %v", err)
	}

	return performance, nil
}

// GetSizeDistribution - Phân tích phân bố kích thước
func (s *DashboardService) GetSizeDistribution(ctx context.Context, days int) ([]types.SizeDistribution, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var distribution []types.SizeDistribution
	query := `
		SELECT
			CASE
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 100 THEN 'Small (≤100MB)'
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 1000 THEN 'Medium (100MB-1GB)'
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 10000 THEN 'Large (1GB-10GB)'
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 50000 THEN 'Very Large (10GB-50GB)'
				ELSE 'Huge (>50GB)'
			END as SizeCategory,

			COUNT(*) as RestoreCount,
			SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) as SuccessCount,
			CAST(SUM(CASE WHEN isSuccess = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as SuccessRate,

			AVG(TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT)) as AvgSizeMB,
			MIN(TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT)) as MinSizeMB,
			MAX(TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT)) as MaxSizeMB

		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE DateTimeRestore >= DATEADD(DAY, -?, GETDATE())
			AND TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) IS NOT NULL
		GROUP BY
			CASE
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 100 THEN 'Small (≤100MB)'
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 1000 THEN 'Medium (100MB-1GB)'
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 10000 THEN 'Large (1GB-10GB)'
				WHEN TRY_CAST(REPLACE(SizeBAK, 'MB', '') AS FLOAT) <= 50000 THEN 'Very Large (10GB-50GB)'
				ELSE 'Huge (>50GB)'
			END
		ORDER BY AvgSizeMB
	`

	if err := db.Raw(query, days).Scan(&distribution).Error; err != nil {
		return nil, fmt.Errorf("failed to execute size distribution query: %v", err)
	}

	return distribution, nil
}

// =============================================
// ALERTS & MONITORING SERVICES
// =============================================

// GetAlerts - Lấy danh sách cảnh báo
func (s *DashboardService) GetAlerts(ctx context.Context) ([]types.Alert, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var alerts []types.Alert
	query := `
		SELECT
			'RECENT_FAILURES' as AlertType,
			'HIGH' as Priority,
			'Restore failures trong 24h qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[ToolRestoreSQLDatabase]
		WHERE isSuccess = '0'
			AND DateTimeRestore >= DATEADD(HOUR, -24, GETDATE())
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'HIGH_FAILURE_RATE' as AlertType,
			'MEDIUM' as Priority,
			'Database có tỷ lệ thất bại cao (>30%) trong 7 ngày qua' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName, ', ') as Details,
			GETDATE() as AlertTime
		FROM (
			SELECT
				DatabaseName,
				COUNT(*) as TotalRestores,
				SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) as FailureCount,
				CAST(SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
			FROM [dbo].[ToolRestoreSQLDatabase]
			WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())
			GROUP BY DatabaseName
			HAVING COUNT(*) >= 3 AND (SUM(CASE WHEN isSuccess = '0' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) > 30
		) HighFailureDb
		HAVING COUNT(*) > 0

		UNION ALL

		SELECT
			'UNUSED_CONFIGS' as AlertType,
			'LOW' as Priority,
			'Cấu hình setup chưa được sử dụng' as Title,
			COUNT(*) as Count,
			STRING_AGG(DatabaseName + ' (' + IP + ')', ', ') as Details,
			GETDATE() as AlertTime
		FROM [dbo].[SetupToolRestoreSQLDatabase] s
		LEFT JOIN [dbo].[ToolRestoreSQLDatabase] t ON s.IP = t.IP AND s.DatabaseName = t.DatabaseName
		WHERE t.ID IS NULL
			AND s.DateTimeRestore <= DATEADD(DAY, -7, GETDATE())
		HAVING COUNT(*) > 0
	`

	if err := db.Raw(query).Scan(&alerts).Error; err != nil {
		return nil, fmt.Errorf("failed to execute alerts query: %v", err)
	}

	return alerts, nil
}

// GetSystemHealth - Lấy thông tin sức khỏe hệ thống
func (s *DashboardService) GetSystemHealth(ctx context.Context) (*types.SystemHealth, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var health types.SystemHealth
	query := `
		SELECT
			'SYSTEM_HEALTH' as CheckType,

			-- Restore health
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as RestoresLast24h,

			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE isSuccess = '1' AND DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as SuccessfulRestoresLast24h,

			-- Setup health
			(SELECT COUNT(*) FROM [dbo].[SetupToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -1, GETDATE())) as NewConfigsLast24h,

			-- Active servers
			(SELECT COUNT(DISTINCT IP) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE DateTimeRestore >= DATEADD(DAY, -7, GETDATE())) as ActiveServersLast7Days,

			-- Data quality
			(SELECT COUNT(*) FROM [dbo].[ToolRestoreSQLDatabase]
			 WHERE SizeBAK IS NULL OR SizeBAK = '' OR DateTimeRestore IS NULL) as DataQualityIssues,

			GETDATE() as CheckTime
	`

	if err := db.Raw(query).Scan(&health).Error; err != nil {
		return nil, fmt.Errorf("failed to execute system health query: %v", err)
	}

	return &health, nil
}

// =============================================
// DETAILED ANALYSIS SERVICES
// =============================================

// GetRestoreDetails - Lấy chi tiết restore với pagination
func (s *DashboardService) GetRestoreDetails(ctx context.Context, params types.RestoreDetailsParams) (*types.PaginatedRestoreDetails, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	// Set default values
	if params.Days == 0 {
		params.Days = 30
	}
	if params.Page == 0 {
		params.Page = 1
	}
	if params.Size == 0 {
		params.Size = 50
	}
	if params.SortBy == "" {
		params.SortBy = "DateTimeRestore"
	}
	if params.SortOrder == "" {
		params.SortOrder = "DESC"
	}

	// Calculate offset
	offset := (params.Page - 1) * params.Size

	// Build WHERE clause
	whereClause := "WHERE t.DateTimeRestore >= DATEADD(DAY, -?, GETDATE())"
	queryParams := []interface{}{params.Days}

	if params.DatabaseName != "" {
		whereClause += " AND t.DatabaseName LIKE ?"
		queryParams = append(queryParams, "%"+params.DatabaseName+"%")
	}
	if params.IP != "" {
		whereClause += " AND t.IP = ?"
		queryParams = append(queryParams, params.IP)
	}
	if params.Status != "" {
		statusValue := "1"
		if params.Status == "failed" || params.Status == "0" {
			statusValue = "0"
		}
		whereClause += " AND t.isSuccess = ?"
		queryParams = append(queryParams, statusValue)
	}

	// Main query
	var details []types.RestoreDetail
	query := fmt.Sprintf(`
		SELECT
			t.ID,
			t.IP,
			t.DatabaseName,
			t.SizeFileZip,
			t.SizeBAK,
			t.DateTimeRestore,
			CASE WHEN t.isSuccess = '1' THEN 'Success' ELSE 'Failed' END as Status,

			-- Compression ratio
			CASE
				WHEN TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT) > 0
					 AND TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT) > 0
				THEN CAST(((TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT) -
							TRY_CAST(REPLACE(t.SizeFileZip, 'MB', '') AS FLOAT)) * 100.0 /
						   TRY_CAST(REPLACE(t.SizeBAK, 'MB', '') AS FLOAT)) AS DECIMAL(5,2))
				ELSE 0
			END as CompressionRatio,

			-- Setup info if available
			ISNULL(s.ServerSQL, '') as ServerSQL,
			CASE WHEN s.isAuto = '1' THEN 'Auto' ELSE 'Manual' END as ConfigType,
			ISNULL(s.Destination_folder, '') as DestinationFolder,

			-- Log summary
			CASE
				WHEN LEN(t.LogContent) > 100 THEN LEFT(t.LogContent, 100) + '...'
				ELSE ISNULL(t.LogContent, '')
			END as LogSummary,

			-- Time since restore
			DATEDIFF(HOUR, t.DateTimeRestore, GETDATE()) as HoursSinceRestore

		FROM [dbo].[ToolRestoreSQLDatabase] t
		LEFT JOIN [dbo].[SetupToolRestoreSQLDatabase] s
			ON t.IP = s.IP AND t.DatabaseName = s.DatabaseName
		%s
		ORDER BY t.%s %s
		OFFSET ? ROWS FETCH NEXT ? ROWS ONLY
	`, whereClause, params.SortBy, params.SortOrder)

	queryParams = append(queryParams, offset, params.Size)

	if err := db.Raw(query, queryParams...).Scan(&details).Error; err != nil {
		return nil, fmt.Errorf("failed to execute restore details query: %v", err)
	}

	// Count query for pagination
	var totalRecords int64
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM [dbo].[ToolRestoreSQLDatabase] t
		%s
	`, whereClause)

	countParams := queryParams[:len(queryParams)-2] // Remove offset and limit params
	if err := db.Raw(countQuery, countParams...).Scan(&totalRecords).Error; err != nil {
		return nil, fmt.Errorf("failed to execute count query: %v", err)
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalRecords) / float64(params.Size)))

	result := &types.PaginatedRestoreDetails{
		Data: details,
		Pagination: types.PaginationInfo{
			TotalRecords: int(totalRecords),
			TotalPages:   totalPages,
			CurrentPage:  params.Page,
			PageSize:     params.Size,
		},
	}

	return result, nil
}
