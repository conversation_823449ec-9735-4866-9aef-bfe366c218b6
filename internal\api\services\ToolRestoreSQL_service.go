package services

import (
	"context"
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type ToolRestoreServices struct {
	*BaseService
}

var ToolRestore = &ToolRestoreServices{}


func (s *ToolRestoreServices) GetRestoreSQLDatabase(ctx context.Context) ([]types.ToolRestoreSQLDatabase, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}
	var statistics []types.ToolRestoreSQLDatabase
	query := `
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			IP,
			DatabaseName,
			CONVERT(VARCHAR(10), DateTimeRestore, 120) as DateTimeRestore,
			SizeFileZip,
			SizeBAK,
			isSuccess,
			LogContent
		FROM ToolRestoreSQLDatabase
		ORDER BY DateTimeRestore DESC
	`

	if err := db.Raw(query).Scan(&statistics).Error; err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	return statistics, nil
}

func (s *ToolRestoreServices) InsertRestoreSQLDatabase(req *types.ToolRestoreSQLDatabase) (string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var ID string
	query := `
		INSERT INTO ToolRestoreSQLDatabase
		(
			ID,
			IP,
			DatabaseName,
			SizeFileZip,
			SizeBAK,
			DateTimeRestore,
			isSuccess,
			LogContent
		)
			OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
			VALUES (NEWID(), ? , ? , ? , ? , GetDate() , ? , ?);
		`
		if err := tx.Raw(
		query,
		req.IP,
		req.DatabaseName,
		req.SizeFileZip,
		req.SizeBAK,
		req.IsSuccess,
		req.LogContent,
		).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}