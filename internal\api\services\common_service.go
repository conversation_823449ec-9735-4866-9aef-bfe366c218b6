package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type CommonService struct {
	*BaseService
}

var Common = &CommonService{}

func (s *CommonService) Login(params *types.User) (any, error) {
	var user types.User

	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	query := `
	SELECT 
		CAST(USERID AS NVARCHAR(36)) AS UserID,
		USERNAME AS UserName,
		PWD AS Password,
		Role
	FROM [ERP_23_9].[LIY_ERP].[dbo].[BUSERS]
	WHERE USERID = ?
`

	err = db.Raw(query, params.UserID).Scan(&user).Error

	err = db.Raw(query, params.UserID).Scan(&user).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	// Không tìm thấy người dùng
	if user.UserID == "" {
		return "Account does not exist or has been locked", nil
	}

	// So sánh mật khẩu đơn giản (nếu chưa hash)
	if user.Password != params.Password {
		return "Password is incorrect", nil
	}

	// Gán token (giả lập token)
	user.Token = fmt.Sprintf("token_%s", user.UserID)
	user.Password = "" // clear password khi trả về

	return user, nil
}
