package types

import "time"

// =============================================
// DASHBOARD RESPONSE TYPES
// =============================================

// OverviewStats - Thống kê tổng quan hệ thống
type OverviewStats struct {
	TotalRestoreOperations      int     `json:"totalRestoreOperations"`
	SuccessfulRestores          int     `json:"successfulRestores"`
	FailedRestores              int     `json:"failedRestores"`
	SuccessRate                 float64 `json:"successRate"`
	TotalSetupConfigs           int     `json:"totalSetupConfigs"`
	AutoConfigs                 int     `json:"autoConfigs"`
	ManualConfigs               int     `json:"manualConfigs"`
	UniqueServersWithRestores   int     `json:"uniqueServersWithRestores"`
	UniqueServersWithSetup      int     `json:"uniqueServersWithSetup"`
	UniqueDatabasesRestored     int     `json:"uniqueDatabasesRestored"`
	UniqueDatabasesConfigured   int     `json:"uniqueDatabasesConfigured"`
}

// RecentActivity - Hoạt động gần đây
type RecentActivity struct {
	ActivityType string `json:"activityType"`
	Count        int    `json:"count"`
	SuccessCount int    `json:"successCount"`
	FailureCount int    `json:"failureCount"`
}

// DailyTrend - Xu hướng theo ngày
type DailyTrend struct {
	Date              string  `json:"date"`
	RestoreDate       string  `json:"restoreDate"`
	TotalRestores     int     `json:"totalRestores"`
	SuccessCount      int     `json:"successCount"`
	FailureCount      int     `json:"failureCount"`
	DailySuccessRate  float64 `json:"dailySuccessRate"`
	AvgSizeBAK        float64 `json:"avgSizeBAK"`
	AvgSizeZip        float64 `json:"avgSizeZip"`
}

// TopDatabase - Database được restore nhiều nhất
type TopDatabase struct {
	DatabaseName           string    `json:"databaseName"`
	RestoreCount           int       `json:"restoreCount"`
	SuccessCount           int       `json:"successCount"`
	FailureCount           int       `json:"failureCount"`
	SuccessRate            float64   `json:"successRate"`
	AvgSizeBAK             float64   `json:"avgSizeBAK"`
	MaxSizeBAK             float64   `json:"maxSizeBAK"`
	LastRestoreTime        time.Time `json:"lastRestoreTime"`
	HoursSinceLastRestore  int       `json:"hoursSinceLastRestore"`
}

// ServerStats - Thống kê theo Server/IP
type ServerStats struct {
	IP                   string    `json:"ip"`
	TotalRestores        int       `json:"totalRestores"`
	SuccessCount         int       `json:"successCount"`
	FailureCount         int       `json:"failureCount"`
	SuccessRate          float64   `json:"successRate"`
	UniqueDbCount        int       `json:"uniqueDbCount"`
	TotalRestoredSizeMB  float64   `json:"totalRestoredSizeMB"`
	FirstRestore         time.Time `json:"firstRestore"`
	LastRestore          time.Time `json:"lastRestore"`
}

// SetupConfigStats - Thống kê Setup Configuration
type SetupConfigStats struct {
	IP                  string    `json:"ip"`
	ServerSQL           string    `json:"serverSQL"`
	ConfigCount         int       `json:"configCount"`
	AutoConfigCount     int       `json:"autoConfigCount"`
	ManualConfigCount   int       `json:"manualConfigCount"`
	AutoPercentage      float64   `json:"autoPercentage"`
	UniqueDbConfigured  int       `json:"uniqueDbConfigured"`
	FirstConfigTime     time.Time `json:"firstConfigTime"`
	LastConfigTime      time.Time `json:"lastConfigTime"`
	ActualRestoreCount  int       `json:"actualRestoreCount"`
}

// ConfigUsageComparison - So sánh Setup vs Actual Restore
type ConfigUsageComparison struct {
	Status       string    `json:"status"`
	IP           string    `json:"ip"`
	DatabaseName string    `json:"databaseName"`
	ServerSQL    string    `json:"serverSQL"`
	ConfigTime   time.Time `json:"configTime"`
	ConfigType   string    `json:"configType"`
}

// ConfigEffectiveness - Hiệu quả cấu hình
type ConfigEffectiveness struct {
	IP                   string    `json:"ip"`
	DatabaseName         string    `json:"databaseName"`
	ServerSQL            string    `json:"serverSQL"`
	ConfigType           string    `json:"configType"`
	ConfigTime           time.Time `json:"configTime"`
	RestoreCount         int       `json:"restoreCount"`
	SuccessfulRestores   int       `json:"successfulRestores"`
	EffectivenessRating  string    `json:"effectivenessRating"`
	SuccessRate          float64   `json:"successRate"`
}

// HourlyPerformance - Hiệu suất theo giờ
type HourlyPerformance struct {
	HourOfDay    int     `json:"hourOfDay"`
	HourLabel    string  `json:"hourLabel"`
	RestoreCount int     `json:"restoreCount"`
	SuccessCount int     `json:"successCount"`
	FailureCount int     `json:"failureCount"`
	AvgSizeBAK   float64 `json:"avgSizeBAK"`
	TimePeriod   string  `json:"timePeriod"`
}

// SizeDistribution - Phân bố kích thước
type SizeDistribution struct {
	SizeCategory string  `json:"sizeCategory"`
	RestoreCount int     `json:"restoreCount"`
	SuccessCount int     `json:"successCount"`
	SuccessRate  float64 `json:"successRate"`
	AvgSizeMB    float64 `json:"avgSizeMB"`
	MinSizeMB    float64 `json:"minSizeMB"`
	MaxSizeMB    float64 `json:"maxSizeMB"`
}

// Alert - Cảnh báo hệ thống
type Alert struct {
	AlertType string    `json:"alertType"`
	Priority  string    `json:"priority"`
	Title     string    `json:"title"`
	Count     int       `json:"count"`
	Details   string    `json:"details"`
	AlertTime time.Time `json:"alertTime"`
}

// SystemHealth - Sức khỏe hệ thống
type SystemHealth struct {
	CheckType                    string    `json:"checkType"`
	RestoresLast24h              int       `json:"restoresLast24h"`
	SuccessfulRestoresLast24h    int       `json:"successfulRestoresLast24h"`
	NewConfigsLast24h            int       `json:"newConfigsLast24h"`
	ActiveServersLast7Days       int       `json:"activeServersLast7Days"`
	DataQualityIssues            int       `json:"dataQualityIssues"`
	CheckTime                    time.Time `json:"checkTime"`
}

// RestoreDetail - Chi tiết restore với pagination
type RestoreDetail struct {
	ID                    string    `json:"id"`
	IP                    string    `json:"ip"`
	DatabaseName          string    `json:"databaseName"`
	SizeFileZip           string    `json:"sizeFileZip"`
	SizeBAK               string    `json:"sizeBAK"`
	DateTimeRestore       time.Time `json:"dateTimeRestore"`
	Status                string    `json:"status"`
	CompressionRatio      float64   `json:"compressionRatio"`
	ServerSQL             string    `json:"serverSQL"`
	ConfigType            string    `json:"configType"`
	DestinationFolder     string    `json:"destinationFolder"`
	LogSummary            string    `json:"logSummary"`
	HoursSinceRestore     int       `json:"hoursSinceRestore"`
}

// PaginationInfo - Thông tin phân trang
type PaginationInfo struct {
	TotalRecords int `json:"totalRecords"`
	TotalPages   int `json:"totalPages"`
	CurrentPage  int `json:"currentPage"`
	PageSize     int `json:"pageSize"`
}

// PaginatedRestoreDetails - Kết quả có phân trang
type PaginatedRestoreDetails struct {
	Data       []RestoreDetail `json:"data"`
	Pagination PaginationInfo  `json:"pagination"`
}

// =============================================
// REQUEST TYPES
// =============================================

// DashboardQueryParams - Parameters chung cho dashboard queries
type DashboardQueryParams struct {
	Days  int `form:"days" json:"days" binding:"omitempty,min=1,max=365"`
	Limit int `form:"limit" json:"limit" binding:"omitempty,min=1,max=100"`
}

// PaginationParams - Parameters cho phân trang
type PaginationParams struct {
	Page int `form:"page" json:"page" binding:"omitempty,min=1"`
	Size int `form:"size" json:"size" binding:"omitempty,min=1,max=100"`
}

// RestoreDetailsParams - Parameters cho chi tiết restore
type RestoreDetailsParams struct {
	DashboardQueryParams
	PaginationParams
	DatabaseName string `form:"database" json:"database"`
	IP           string `form:"ip" json:"ip"`
	Status       string `form:"status" json:"status"`
	SortBy       string `form:"sort" json:"sort"`
	SortOrder    string `form:"order" json:"order"`
}
